apiVersion: v1
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system
data:
  Corefile: |
    .:53 {
        # Add hosts entry to redirect Tencent mirrors to Docker Hub
        hosts {
            registry-1.docker.io mirror.ccs.tencentyun.com
            registry-1.docker.io saoccr.ccs.tencentyun.com
            fallthrough
        }
        errors
        health {
            lameduck 30s
        }
        ready
        kubernetes cluster.local. in-addr.arpa ip6.arpa {
            pods insecure
            fallthrough in-addr.arpa ip6.arpa
        }
        prometheus :9153
        forward . /etc/resolv.conf {
            prefer_udp
        }
        cache 30
        reload
        loadbalance
    }
