compartment_name    = "Digisac-Cluster"
vcn_name            = "vcn-digisac-cluster"
region              = "sa-vinhedo-1"
vcn_cidr            = "10.180.0.0/16"
vpn_cidr            = "10.10.255.4/32"
tenancy_namespace   = "axvaplbwrlcl"
tenancy_ocid        = "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa"
user_ocid           = "ocid1.user.oc1..aaaaaaaaeuyxmxdbk57x4kq2ktim2kbc5d2dhx5x3kqqx73nd3ceczeag5tq"
container_images_compartment_ocid = "ocid1.compartment.oc1..aaaaaaaadnapi3elokblvktxtq52kryjjqjhuk5zeqj6ojmz5pecblc3lrka" # Digisac/Prod
fingerprint         = "61:67:a5:72:37:e7:90:44:35:f5:a3:56:be:bc:27:7c"
private_key_path    = "~/.oci/<EMAIL>.br_2025-02-27T23_58_23.878Z.pem"
ssh_public_key_path = "~/.oci/<EMAIL>.br_2025-02-27T23_58_23.878Z.pub"
availability_domain = "Ixbb:SA-VINHEDO-1-AD-1"
kubernetes_version  = "v1.31.1"

# oci ce node-pool-options get --node-pool-option-id all --compartment-id $TENANCY_OCID
node_image_id = "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq"

# postgres
postgres_playbook_run = true
postgres_image_id = "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa26nbjofrqahy4b47mjegokq5rut6k3l7g2mzbsxxhw7bvbydbx6a"
postgres_user     = "postgres"
postgres_db       = "digisac"
postgres_password = "c4KfVN2Y6OclDw8ybF1K0RKxzlE7UQVV"