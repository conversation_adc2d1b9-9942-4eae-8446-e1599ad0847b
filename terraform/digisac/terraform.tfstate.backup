{"version": 4, "terraform_version": "1.11.0", "serial": 327, "lineage": "b45ccea4-16bc-fc27-465d-874aad6e9cc1", "outputs": {}, "resources": [{"module": "module.cluster-1", "mode": "managed", "type": "local_file", "name": "cluster_autoscaler_values", "provider": "provider[\"registry.terraform.io/hashicorp/local\"]", "instances": [{"schema_version": 0, "attributes": {"content": "\"pools\":\n- \"id\": \"ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaakcor2ijopgfna3xzlvztkxyqr5squ5keetjuurkaqno6evf5zmoa\"\n  \"maxNodes\": 10\n  \"minNodes\": 1\n  \"name\": \"digisac-cluster-1-sl-spot\"\n  \"priority\": 10\n- \"id\": \"ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaalbpow3rdfl6gt5ymkklyeek553toe3emmjd2wu2vqn2ypprbqpja\"\n  \"maxNodes\": 10\n  \"minNodes\": 0\n  \"name\": \"digisac-cluster-1-sl-fallback\"\n  \"priority\": 100\n- \"id\": \"ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaahh3la2pdjfvxubhdsox7s7xzan7diemjffxu3ax57nakxa7uffka\"\n  \"maxNodes\": 10\n  \"minNodes\": 1\n  \"name\": \"digisac-cluster-1-sf\"\n  \"priority\": 50\n- \"id\": \"ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaaa4ntui7zqh2mgjr4vz4viovfusla2slga2jbgopkancybhzpwesq\"\n  \"maxNodes\": 5\n  \"minNodes\": 1\n  \"name\": \"digisac-cluster-1-tools\"\n  \"priority\": 50\n\"priorityExpander\":\n  \"enabled\": true\n", "content_base64": null, "content_base64sha256": "aTy4JjFQCcizsCU1iwelgTGu+noiKfhqtW3ZkSqB+GM=", "content_base64sha512": "FUS5UuZyw0Bbwy9N6wVjo3Dt3Cke2mIwKw9VVEK1GnfYI/5UZnLow+wRuYYcUeMwp4vyj7q6c55VLuIfG9gmLA==", "content_md5": "315ca9d3bd02276c5f47d12b486a6e0a", "content_sha1": "6f45db4f5826f3be31f9cf7d61281028886233e0", "content_sha256": "693cb826315009c8b3b025358b07a58131aefa7a2229f86ab56dd9912a81f863", "content_sha512": "1544b952e672c3405bc32f4deb0563a370eddc291eda62302b0f555442b51a77d823fe546672e8c3ec11b9861c51e330a78bf28fbaba739e552ee21f1bd8262c", "directory_permission": "0777", "file_permission": "0777", "filename": "../kubernetes/tools/oracle-cluster-autoscaler/digisac-cluster-1-values.yaml", "id": "6f45db4f5826f3be31f9cf7d61281028886233e0", "sensitive_content": null, "source": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "sensitive_content"}]], "dependencies": ["module.cluster-1.oci_containerengine_cluster.digisac_cluster", "module.cluster-1.oci_containerengine_node_pool.digisac_statefull", "module.cluster-1.oci_containerengine_node_pool.digisac_stateless_fallback", "module.cluster-1.oci_containerengine_node_pool.digisac_stateless_spot", "module.cluster-1.oci_containerengine_node_pool.digisac_tools", "module.cluster-1.oci_core_internet_gateway.digisac_ig", "module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.cluster-1.oci_core_route_table.digisac_private_rt", "module.cluster-1.oci_core_route_table.digisac_public_rt", "module.cluster-1.oci_core_security_list.digisac_private_sl", "module.cluster-1.oci_core_security_list.digisac_public_sl", "module.cluster-1.oci_core_subnet.digisac_private_subnet", "module.cluster-1.oci_core_subnet.digisac_public_subnet", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_containerengine_cluster", "name": "digisac_cluster", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"available_kubernetes_upgrades": [], "cluster_pod_network_options": [{"cni_type": "FLANNEL_OVERLAY"}], "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T23:22:12.234Z"}, "endpoint_config": [{"is_public_ip_enabled": false, "nsg_ids": [], "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}], "endpoints": [{"ipv6endpoint": "", "kubernetes": "", "private_endpoint": "**********32:6443", "public_endpoint": "", "vcn_hostname_endpoint": "cmd4lexqxnq.private.digisacvcn.oraclevcn.com:6443"}], "freeform_tags": {}, "id": "ocid1.cluster.oc1.sa-vinhedo-1.aaaaaaa<PERSON>dirowi5ggqu6fjmkg5mofwotmqxbxtg6qkuwsazhcmd4lexqxnq", "image_policy_config": [{"is_policy_enabled": false, "key_details": []}], "kms_key_id": null, "kubernetes_version": "v1.31.1", "lifecycle_details": null, "metadata": [{"created_by_user_id": "ocid1.user.oc1..aaaaaaaaeuyxmxdbk57x4kq2ktim2kbc5d2dhx5x3kqqx73nd3ceczeag5tq", "created_by_work_request_id": "ocid1.clustersworkrequest.oc1.sa-vinhedo-1.aaaaaaaatpszgehqfaszrg6gbztcggtmh2rdv7vpigkjqniqowotflhu4ala", "deleted_by_user_id": "", "deleted_by_work_request_id": "", "time_created": "2025-02-28 23:22:12 +0000 UTC", "time_credential_expiration": "2030-02-28 23:22:46 +0000 UTC", "time_deleted": "", "time_updated": "", "updated_by_user_id": "", "updated_by_work_request_id": ""}], "name": "digisac-cluster-1", "open_id_connect_discovery_endpoint": null, "options": [{"add_ons": [{"is_kubernetes_dashboard_enabled": true, "is_tiller_enabled": false}], "admission_controller_options": [{"is_pod_security_policy_enabled": false}], "ip_families": ["IPv4"], "kubernetes_network_config": [{"pods_cidr": "**********/16", "services_cidr": "*********/16"}], "open_id_connect_discovery": [], "open_id_connect_token_authentication_config": [], "persistent_volume_config": [{"defined_tags": {}, "freeform_tags": {}}], "service_lb_config": [{"defined_tags": {}, "freeform_tags": {}}], "service_lb_subnet_ids": ["ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaatubvgf3mtejucf2yr5yrzdet7ipfcdp5wki27m5qa7exl7y4ydqa"]}], "state": "ACTIVE", "timeouts": null, "type": "ENHANCED_CLUSTER", "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInVwZGF0ZSI6MzYwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_core_internet_gateway.digisac_ig", "module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.cluster-1.oci_core_route_table.digisac_private_rt", "module.cluster-1.oci_core_route_table.digisac_public_rt", "module.cluster-1.oci_core_security_list.digisac_private_sl", "module.cluster-1.oci_core_security_list.digisac_public_sl", "module.cluster-1.oci_core_subnet.digisac_private_subnet", "module.cluster-1.oci_core_subnet.digisac_public_subnet", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_containerengine_node_pool", "name": "digisac_statefull", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"cluster_id": "ocid1.cluster.oc1.sa-vinhedo-1.aaaaaaa<PERSON>dirowi5ggqu6fjmkg5mofwotmqxbxtg6qkuwsazhcmd4lexqxnq", "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T23:28:52.180Z"}, "freeform_tags": {}, "id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaahh3la2pdjfvxubhdsox7s7xzan7diemjffxu3ax57nakxa7uffka", "initial_node_labels": [{"key": "pool", "value": "statefull"}], "kubernetes_version": "v1.31.1", "lifecycle_details": null, "name": "digisac-cluster-1-sf", "node_config_details": [{"defined_tags": {}, "freeform_tags": {}, "is_pv_encryption_in_transit_enabled": false, "kms_key_id": "", "node_pool_pod_network_option_details": [{"cni_type": "FLANNEL_OVERLAY", "max_pods_per_node": 0, "pod_nsg_ids": [], "pod_subnet_ids": []}], "nsg_ids": [], "placement_configs": [{"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "capacity_reservation_id": "", "fault_domains": [], "preemptible_node_config": [], "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}], "size": 1}], "node_eviction_node_pool_settings": [], "node_image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "node_image_name": "Oracle-Linux-8.10-2024.11.30-0-OKE-1.31.1-754", "node_metadata": {"pool": "statefull"}, "node_pool_cycling_details": [], "node_shape": "VM.Standard.E4.Flex", "node_shape_config": [{"memory_in_gbs": 16, "ocpus": 4}], "node_source": [{"image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "source_name": "Oracle-Linux-8.10-2024.11.30-0-OKE-1.31.1-754", "source_type": "IMAGE"}], "node_source_details": [{"boot_volume_size_in_gbs": "", "image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "source_type": "IMAGE"}], "nodes": [{"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "defined_tags": {}, "error": [], "fault_domain": "FAULT-DOMAIN-1", "freeform_tags": {}, "id": "ocid1.instance.oc1.sa-vinhedo-1.an3ggljrsfii6fich7ksaeuj5zgjwuvnyhbhyr27q6ntpzfk3wmrgy2mhldq", "kubernetes_version": "v1.31.1", "lifecycle_details": "", "name": "oke-cmd4lexqxnq-nakxa7uffka-stuoworxkdq-0", "node_pool_id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaahh3la2pdjfvxubhdsox7s7xzan7diemjffxu3ax57nakxa7uffka", "private_ip": "************", "public_ip": "", "state": "ACTIVE", "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}], "quantity_per_subnet": 0, "ssh_public_key": null, "state": "ACTIVE", "subnet_ids": ["ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"], "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAwLCJkZWxldGUiOjMwMDAwMDAwMDAwMDAsInVwZGF0ZSI6MzAwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_containerengine_cluster.digisac_cluster", "module.cluster-1.oci_core_internet_gateway.digisac_ig", "module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.cluster-1.oci_core_route_table.digisac_private_rt", "module.cluster-1.oci_core_route_table.digisac_public_rt", "module.cluster-1.oci_core_security_list.digisac_private_sl", "module.cluster-1.oci_core_security_list.digisac_public_sl", "module.cluster-1.oci_core_subnet.digisac_private_subnet", "module.cluster-1.oci_core_subnet.digisac_public_subnet", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_containerengine_node_pool", "name": "digisac_stateless_fallback", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"cluster_id": "ocid1.cluster.oc1.sa-vinhedo-1.aaaaaaa<PERSON>dirowi5ggqu6fjmkg5mofwotmqxbxtg6qkuwsazhcmd4lexqxnq", "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T23:28:52.121Z"}, "freeform_tags": {}, "id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaalbpow3rdfl6gt5ymkklyeek553toe3emmjd2wu2vqn2ypprbqpja", "initial_node_labels": [{"key": "pool", "value": "stateless-fallback"}], "kubernetes_version": "v1.31.1", "lifecycle_details": null, "name": "digisac-cluster-1-sl-fallback", "node_config_details": [{"defined_tags": {}, "freeform_tags": {}, "is_pv_encryption_in_transit_enabled": false, "kms_key_id": "", "node_pool_pod_network_option_details": [{"cni_type": "FLANNEL_OVERLAY", "max_pods_per_node": 0, "pod_nsg_ids": [], "pod_subnet_ids": []}], "nsg_ids": [], "placement_configs": [{"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "capacity_reservation_id": "", "fault_domains": [], "preemptible_node_config": [], "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}], "size": 0}], "node_eviction_node_pool_settings": [], "node_image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "node_image_name": "Oracle-Linux-8.10-2024.11.30-0-OKE-1.31.1-754", "node_metadata": {"pool": "stateless-fallback"}, "node_pool_cycling_details": [], "node_shape": "VM.Standard.E4.Flex", "node_shape_config": [{"memory_in_gbs": 16, "ocpus": 4}], "node_source": [{"image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "source_name": "Oracle-Linux-8.10-2024.11.30-0-OKE-1.31.1-754", "source_type": "IMAGE"}], "node_source_details": [{"boot_volume_size_in_gbs": "", "image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "source_type": "IMAGE"}], "nodes": [{"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "defined_tags": {}, "error": [], "fault_domain": "FAULT-DOMAIN-1", "freeform_tags": {}, "id": "ocid1.instance.oc1.sa-vinhedo-1.an3ggljrsfii6fic6ztm26gd72dn46fw7sbpabwqpgol4tprq6zmwv7q7jcq", "kubernetes_version": "v1.31.1", "lifecycle_details": "", "name": "oke-cmd4lexqxnq-n2ypprbqpja-stuoworxkdq-0", "node_pool_id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaalbpow3rdfl6gt5ymkklyeek553toe3emmjd2wu2vqn2ypprbqpja", "private_ip": "************", "public_ip": "", "state": "DELETED", "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}], "quantity_per_subnet": 0, "ssh_public_key": null, "state": "ACTIVE", "subnet_ids": ["ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"], "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAwLCJkZWxldGUiOjMwMDAwMDAwMDAwMDAsInVwZGF0ZSI6MzAwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_containerengine_cluster.digisac_cluster", "module.cluster-1.oci_core_internet_gateway.digisac_ig", "module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.cluster-1.oci_core_route_table.digisac_private_rt", "module.cluster-1.oci_core_route_table.digisac_public_rt", "module.cluster-1.oci_core_security_list.digisac_private_sl", "module.cluster-1.oci_core_security_list.digisac_public_sl", "module.cluster-1.oci_core_subnet.digisac_private_subnet", "module.cluster-1.oci_core_subnet.digisac_public_subnet", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_containerengine_node_pool", "name": "digisac_stateless_spot", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"cluster_id": "ocid1.cluster.oc1.sa-vinhedo-1.aaaaaaa<PERSON>dirowi5ggqu6fjmkg5mofwotmqxbxtg6qkuwsazhcmd4lexqxnq", "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T23:28:52.180Z"}, "freeform_tags": {}, "id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaakcor2ijopgfna3xzlvztkxyqr5squ5keetjuurkaqno6evf5zmoa", "initial_node_labels": [{"key": "pool", "value": "stateless-spot"}], "kubernetes_version": "v1.31.1", "lifecycle_details": null, "name": "digisac-cluster-1-sl-spot", "node_config_details": [{"defined_tags": {}, "freeform_tags": {}, "is_pv_encryption_in_transit_enabled": false, "kms_key_id": "", "node_pool_pod_network_option_details": [{"cni_type": "FLANNEL_OVERLAY", "max_pods_per_node": 0, "pod_nsg_ids": [], "pod_subnet_ids": []}], "nsg_ids": [], "placement_configs": [{"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "capacity_reservation_id": "", "fault_domains": [], "preemptible_node_config": [{"preemption_action": [{"is_preserve_boot_volume": false, "type": "TERMINATE"}]}], "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}], "size": 1}], "node_eviction_node_pool_settings": [{"eviction_grace_duration": "PT0S", "is_force_delete_after_grace_duration": true}], "node_image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "node_image_name": "Oracle-Linux-8.10-2024.11.30-0-OKE-1.31.1-754", "node_metadata": {"pool": "stateless-spot"}, "node_pool_cycling_details": [], "node_shape": "VM.Standard.E4.Flex", "node_shape_config": [{"memory_in_gbs": 16, "ocpus": 4}], "node_source": [{"image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "source_name": "Oracle-Linux-8.10-2024.11.30-0-OKE-1.31.1-754", "source_type": "IMAGE"}], "node_source_details": [{"boot_volume_size_in_gbs": "", "image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "source_type": "IMAGE"}], "nodes": [{"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "defined_tags": {}, "error": [], "fault_domain": "FAULT-DOMAIN-1", "freeform_tags": {}, "id": "ocid1.instance.oc1.sa-vinhedo-1.an3ggljrsfii6ficuep6x55s224ayvszsekg3oxmpomwwr6dljd6khceosdq", "kubernetes_version": "v1.31.1", "lifecycle_details": "", "name": "oke-cmd4lexqxnq-no6evf5zmoa-stuoworxkdq-0", "node_pool_id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaakcor2ijopgfna3xzlvztkxyqr5squ5keetjuurkaqno6evf5zmoa", "private_ip": "************", "public_ip": "", "state": "ACTIVE", "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}], "quantity_per_subnet": 0, "ssh_public_key": null, "state": "ACTIVE", "subnet_ids": ["ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"], "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAwLCJkZWxldGUiOjMwMDAwMDAwMDAwMDAsInVwZGF0ZSI6MzAwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_containerengine_cluster.digisac_cluster", "module.cluster-1.oci_core_internet_gateway.digisac_ig", "module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.cluster-1.oci_core_route_table.digisac_private_rt", "module.cluster-1.oci_core_route_table.digisac_public_rt", "module.cluster-1.oci_core_security_list.digisac_private_sl", "module.cluster-1.oci_core_security_list.digisac_public_sl", "module.cluster-1.oci_core_subnet.digisac_private_subnet", "module.cluster-1.oci_core_subnet.digisac_public_subnet", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_containerengine_node_pool", "name": "digisac_tools", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"cluster_id": "ocid1.cluster.oc1.sa-vinhedo-1.aaaaaaa<PERSON>dirowi5ggqu6fjmkg5mofwotmqxbxtg6qkuwsazhcmd4lexqxnq", "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T23:28:52.121Z"}, "freeform_tags": {}, "id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaaa4ntui7zqh2mgjr4vz4viovfusla2slga2jbgopkancybhzpwesq", "initial_node_labels": [{"key": "pool", "value": "tools"}], "kubernetes_version": "v1.31.1", "lifecycle_details": null, "name": "digisac-cluster-1-tools", "node_config_details": [{"defined_tags": {}, "freeform_tags": {}, "is_pv_encryption_in_transit_enabled": false, "kms_key_id": "", "node_pool_pod_network_option_details": [{"cni_type": "FLANNEL_OVERLAY", "max_pods_per_node": 0, "pod_nsg_ids": [], "pod_subnet_ids": []}], "nsg_ids": [], "placement_configs": [{"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "capacity_reservation_id": "", "fault_domains": [], "preemptible_node_config": [], "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}], "size": 3}], "node_eviction_node_pool_settings": [], "node_image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "node_image_name": "Oracle-Linux-8.10-2024.11.30-0-OKE-1.31.1-754", "node_metadata": {"pool": "tools"}, "node_pool_cycling_details": [], "node_shape": "VM.Standard.E4.Flex", "node_shape_config": [{"memory_in_gbs": 16, "ocpus": 4}], "node_source": [{"image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "source_name": "Oracle-Linux-8.10-2024.11.30-0-OKE-1.31.1-754", "source_type": "IMAGE"}], "node_source_details": [{"boot_volume_size_in_gbs": "", "image_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa3ossfihws7o3yuxuulk6fy66bbgi4igqh2hsbuzzw4a2u4mvp6sq", "source_type": "IMAGE"}], "nodes": [{"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "defined_tags": {}, "error": [], "fault_domain": "FAULT-DOMAIN-1", "freeform_tags": {}, "id": "ocid1.instance.oc1.sa-vinhedo-1.an3ggljrsfii6ficmdtkfmirvz3ixsxbd2dmjwkpaiwqhjg4z2ma5ffuspra", "kubernetes_version": "v1.31.1", "lifecycle_details": "", "name": "oke-cmd4lexqxnq-ncybhzpwesq-stuoworxkdq-0", "node_pool_id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaaa4ntui7zqh2mgjr4vz4viovfusla2slga2jbgopkancybhzpwesq", "private_ip": "**********83", "public_ip": "", "state": "ACTIVE", "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}, {"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "defined_tags": {}, "error": [], "fault_domain": "FAULT-DOMAIN-2", "freeform_tags": {}, "id": "ocid1.instance.oc1.sa-vinhedo-1.an3ggljrsfii6ficnovczoyg3o65n7at5jggkngw2ej25nmdf6q7fhyobq5q", "kubernetes_version": "v1.31.1", "lifecycle_details": "", "name": "oke-cmd4lexqxnq-ncybhzpwesq-stuoworxkdq-1", "node_pool_id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaaa4ntui7zqh2mgjr4vz4viovfusla2slga2jbgopkancybhzpwesq", "private_ip": "***********", "public_ip": "", "state": "ACTIVE", "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}, {"availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "defined_tags": {}, "error": [], "fault_domain": "FAULT-DOMAIN-3", "freeform_tags": {}, "id": "ocid1.instance.oc1.sa-vinhedo-1.an3ggljrsfii6ficoxqbhbhrx4vz7suy4kpkcfitdnq5zynfop6yyp33iqaq", "kubernetes_version": "v1.31.1", "lifecycle_details": "", "name": "oke-cmd4lexqxnq-ncybhzpwesq-stuoworxkdq-2", "node_pool_id": "ocid1.nodepool.oc1.sa-vinhedo-1.aaaaaaaaa4ntui7zqh2mgjr4vz4viovfusla2slga2jbgopkancybhzpwesq", "private_ip": "***********", "public_ip": "", "state": "ACTIVE", "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"}], "quantity_per_subnet": 0, "ssh_public_key": null, "state": "ACTIVE", "subnet_ids": ["ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq"], "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAwLCJkZWxldGUiOjMwMDAwMDAwMDAwMDAsInVwZGF0ZSI6MzAwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_containerengine_cluster.digisac_cluster", "module.cluster-1.oci_core_internet_gateway.digisac_ig", "module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.cluster-1.oci_core_route_table.digisac_private_rt", "module.cluster-1.oci_core_route_table.digisac_public_rt", "module.cluster-1.oci_core_security_list.digisac_private_sl", "module.cluster-1.oci_core_security_list.digisac_public_sl", "module.cluster-1.oci_core_subnet.digisac_private_subnet", "module.cluster-1.oci_core_subnet.digisac_public_subnet", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_internet_gateway", "name": "digisac_ig", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:33.771Z"}, "display_name": "digisac-cluster-1-internet-gateway", "enabled": true, "freeform_tags": {}, "id": "ocid1.internetgateway.oc1.sa-vinhedo-1.aaaaaaaa6skoqhn23pmyf3u2zbgcqmodlnsbn27oqjhqcaulkzwdxq6vfd7a", "route_table_id": null, "state": "AVAILABLE", "time_created": "2025-02-28 00:22:33.813 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMCJ9", "dependencies": ["module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_nat_gateway", "name": "digisac_nat", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"block_traffic": false, "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:33.872Z"}, "display_name": "digisac-cluster-1-nat-gateway", "freeform_tags": {}, "id": "ocid1.natgateway.oc1.sa-vinhedo-1.aaaaaaaa2tbqovfirumqgeowszpllaaxua55w3pvqru3t4ttgubavrpxrjuq", "nat_ip": "**************", "public_ip_id": "ocid1.publicip.oc1.sa-vinhedo-1.aaaaaaaanmog3icblkwim22dsynmvxc3hlrmzhhcbt5h4dg63bjswjnux2ka", "route_table_id": null, "state": "AVAILABLE", "time_created": "2025-02-28 00:22:34.087 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_network_security_group", "name": "cluster_nsg", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:45:40.023Z"}, "display_name": "digisac-cluster-1-cluster-nsg", "freeform_tags": {}, "id": "ocid1.networksecuritygroup.oc1.sa-vinhedo-1.aaaaaaaajc6kuqdisihng4k5odnju4fys2lcwvnx7k4qbukv4fjxnsu5iudq", "state": "AVAILABLE", "time_created": "2025-02-28 00:45:40.147 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_network_security_group_security_rule", "name": "cluster_nsg_rule", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"description": null, "destination": null, "destination_type": "", "direction": "INGRESS", "icmp_options": [], "id": "370904", "is_valid": true, "network_security_group_id": "ocid1.networksecuritygroup.oc1.sa-vinhedo-1.aaaaaaaajc6kuqdisihng4k5odnju4fys2lcwvnx7k4qbukv4fjxnsu5iudq", "protocol": "6", "source": "0.0.0.0/0", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"destination_port_range": [{"max": 6443, "min": 6443}], "source_port_range": []}], "time_created": "2025-02-28 00:45:40.51 +0000 UTC", "timeouts": null, "udp_options": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_core_network_security_group.cluster_nsg", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_route_table", "name": "digisac_private_rt", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:34.940Z"}, "display_name": "digisac-cluster-1-private-route-table", "freeform_tags": {}, "id": "ocid1.routetable.oc1.sa-vinhedo-1.aaaaaaaa2j6vurhuiafvnrx43vhjw26y6ow5owryjacctqdcnqadrwmerkoq", "route_rules": [{"cidr_block": "", "description": "Peering Digisac-Cluster X Tools", "destination": "*********/16", "destination_type": "CIDR_BLOCK", "network_entity_id": "ocid1.localpeeringgateway.oc1.sa-vinhedo-1.aaaaaaaa62kdqmghj2ozbhnqwe36enb2z3g7kixaanmwe53xj3nn4uxghxvq", "route_type": ""}, {"cidr_block": "", "description": "Traffic to the internet", "destination": "0.0.0.0/0", "destination_type": "CIDR_BLOCK", "network_entity_id": "ocid1.natgateway.oc1.sa-vinhedo-1.aaaaaaaa2tbqovfirumqgeowszpllaaxua55w3pvqru3t4ttgubavrpxrjuq", "route_type": ""}], "state": "AVAILABLE", "time_created": "2025-02-28 00:22:34.965 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_route_table", "name": "digisac_public_rt", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:34.303Z"}, "display_name": "digisac-cluster-1-public-route-table", "freeform_tags": {}, "id": "ocid1.routetable.oc1.sa-vinhedo-1.aaaaaaaa6w3zclql2isajxnz3sakfb7hv3qsx6djvp6akvhbfp5wqrioex4a", "route_rules": [{"cidr_block": "", "description": "Internet Gateway", "destination": "0.0.0.0/0", "destination_type": "CIDR_BLOCK", "network_entity_id": "ocid1.internetgateway.oc1.sa-vinhedo-1.aaaaaaaa6skoqhn23pmyf3u2zbgcqmodlnsbn27oqjhqcaulkzwdxq6vfd7a", "route_type": ""}], "state": "AVAILABLE", "time_created": "2025-02-28 00:22:34.329 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_core_internet_gateway.digisac_ig", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_security_list", "name": "digisac_private_sl", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:33.844Z"}, "display_name": "digisac-cluster-1-private-security-list", "egress_security_rules": [{"description": "Allow all outbound traffic", "destination": "0.0.0.0/0", "destination_type": "CIDR_BLOCK", "icmp_options": [], "protocol": "all", "stateless": false, "tcp_options": [], "udp_options": []}], "freeform_tags": {}, "id": "ocid1.securitylist.oc1.sa-vinhedo-1.aaaaaaaagl5s67qmxuqjx5yliz4muttspzsamfqtxpgtdmhf3bgs257dt25a", "ingress_security_rules": [{"description": "Allow pods on one worker node to communicate with pods on other worker nodes", "icmp_options": [], "protocol": "all", "source": "**********/16", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [], "udp_options": []}, {"description": "VPN", "icmp_options": [], "protocol": "6", "source": "***********/32", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [], "udp_options": []}], "state": "AVAILABLE", "time_created": "2025-02-28 00:22:34.018 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_security_list", "name": "digisac_public_sl", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:33.844Z"}, "display_name": "digisac-cluster-1-public-security-list", "egress_security_rules": [{"description": "Allow pods on one worker node to communicate with pods on other worker nodes", "destination": "0.0.0.0/0", "destination_type": "CIDR_BLOCK", "icmp_options": [], "protocol": "all", "stateless": false, "tcp_options": [], "udp_options": []}], "freeform_tags": {}, "id": "ocid1.securitylist.oc1.sa-vinhedo-1.aaaaaaaaez<PERSON>eji7d3dxncjmswc67ncohxaxgwb42h4jmuchzmfn6kaw6ukq", "ingress_security_rules": [{"description": "Allow public ingress for HTTP", "icmp_options": [], "protocol": "6", "source": "0.0.0.0/0", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 80, "min": 80, "source_port_range": []}], "udp_options": []}, {"description": "Istio health check from Oracle's GWLB Endpoints", "icmp_options": [], "protocol": "6", "source": "***********/16", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 15021, "min": 15021, "source_port_range": []}], "udp_options": []}, {"description": "Istio health check from Oracle's GWLB Endpoints", "icmp_options": [], "protocol": "6", "source": "**********/16", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 15021, "min": 15021, "source_port_range": []}], "udp_options": []}, {"description": "Istio health check from Oracle's GWLB Endpoints", "icmp_options": [], "protocol": "6", "source": "**********/16", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 15021, "min": 15021, "source_port_range": []}], "udp_options": []}, {"description": "PostgreSQL Exporter", "icmp_options": [], "protocol": "6", "source": "**********/16", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 9187, "min": 9187, "source_port_range": []}], "udp_options": []}], "state": "AVAILABLE", "time_created": "2025-02-28 00:22:33.881 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_subnet", "name": "digisac_private_subnet", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"availability_domain": null, "cidr_block": "**********/22", "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T20:31:17.734Z"}, "dhcp_options_id": "ocid1.dhcpoptions.oc1.sa-vinhedo-1.aaaaaaaai7bmb5tcphgd3c4deo6qvly53zjbs6gl4aoariztdtrpyfpduxyq", "display_name": "digisac-cluster-1-private-subnet", "dns_label": "private", "freeform_tags": {}, "id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa4ramohz6kjy6ufgjyrtr6jo5tn5g6ig4to7ibfagiatuoworxkdq", "ipv6cidr_block": null, "ipv6cidr_blocks": [], "ipv6virtual_router_ip": null, "prohibit_internet_ingress": true, "prohibit_public_ip_on_vnic": true, "route_table_id": "ocid1.routetable.oc1.sa-vinhedo-1.aaaaaaaa2j6vurhuiafvnrx43vhjw26y6ow5owryjacctqdcnqadrwmerkoq", "security_list_ids": ["ocid1.securitylist.oc1.sa-vinhedo-1.aaaaaaaagl5s67qmxuqjx5yliz4muttspzsamfqtxpgtdmhf3bgs257dt25a"], "state": "AVAILABLE", "subnet_domain_name": "private.digisacvcn.oraclevcn.com", "time_created": "2025-02-28 20:31:17.785 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq", "virtual_router_ip": "**********", "virtual_router_mac": "00:00:17:56:EF:5B"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.cluster-1.oci_core_route_table.digisac_private_rt", "module.cluster-1.oci_core_security_list.digisac_private_sl", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_core_subnet", "name": "digisac_public_subnet", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"availability_domain": null, "cidr_block": "**********/22", "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:34.686Z"}, "dhcp_options_id": "ocid1.dhcpoptions.oc1.sa-vinhedo-1.aaaaaaaai7bmb5tcphgd3c4deo6qvly53zjbs6gl4aoariztdtrpyfpduxyq", "display_name": "digisac-cluster-1-public-subnet", "dns_label": "public", "freeform_tags": {}, "id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaatubvgf3mtejucf2yr5yrzdet7ipfcdp5wki27m5qa7exl7y4ydqa", "ipv6cidr_block": null, "ipv6cidr_blocks": [], "ipv6virtual_router_ip": null, "prohibit_internet_ingress": false, "prohibit_public_ip_on_vnic": false, "route_table_id": "ocid1.routetable.oc1.sa-vinhedo-1.aaaaaaaa6w3zclql2isajxnz3sakfb7hv3qsx6djvp6akvhbfp5wqrioex4a", "security_list_ids": ["ocid1.securitylist.oc1.sa-vinhedo-1.aaaaaaaaez<PERSON>eji7d3dxncjmswc67ncohxaxgwb42h4jmuchzmfn6kaw6ukq"], "state": "AVAILABLE", "subnet_domain_name": "public.digisacvcn.oraclevcn.com", "time_created": "2025-02-28 00:22:34.732 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq", "virtual_router_ip": "**********", "virtual_router_mac": "00:00:17:56:EF:5B"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_core_internet_gateway.digisac_ig", "module.cluster-1.oci_core_route_table.digisac_public_rt", "module.cluster-1.oci_core_security_list.digisac_public_sl", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_identity_dynamic_group", "name": "oke_nodes_dynamic_group", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:30.849Z"}, "description": "Dynamic group for OKE nodes in cluster digisac-cluster-1", "freeform_tags": {}, "id": "ocid1.dynamicgroup.oc1..aaaaaaaa6h2ziw37ibch5n2qits5umzdj7y4bvxmioof66y6rrast4v3dueq", "inactive_state": null, "matching_rule": "ALL {instance.compartment.id = 'ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q'}", "name": "digisac-cluster-1-nodes-dg", "state": "ACTIVE", "time_created": "2025-02-28 00:22:30.9 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1", "mode": "managed", "type": "oci_identity_policy", "name": "oke_autoscaler_policy", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"ETag": "4d3f63a88b92ed5925a5084903cc3ba6fdbe22fc", "compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:31.350Z"}, "description": "Policy to allow OKE nodes to manage node pools for autoscaling", "freeform_tags": {}, "id": "ocid1.policy.oc1..aaaaaaaa3quzhu3nzwpegfd5mvjct6xnwbrxymrkjqxoea5wlnisexlipazq", "inactive_state": null, "lastUpdateETag": "4d3f63a88b92ed5925a5084903cc3ba6fdbe22fc", "name": "digisac-cluster-1-autoscaler-policy", "policyHash": "d864e40c740f6c242f4344a17f547f43", "state": "ACTIVE", "statements": ["Allow dynamic-group digisac-cluster-1-nodes-dg to manage cluster-node-pools in compartment id ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "Allow dynamic-group digisac-cluster-1-nodes-dg to manage instance-family in compartment id ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "Allow dynamic-group digisac-cluster-1-nodes-dg to use subnets in compartment id ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "Allow dynamic-group digisac-cluster-1-nodes-dg to read virtual-network-family in compartment id ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "Allow dynamic-group digisac-cluster-1-nodes-dg to use vnics in compartment id ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "Allow dynamic-group digisac-cluster-1-nodes-dg to inspect compartments in compartment id ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q"], "time_created": "2025-02-28 00:22:31.382 +0000 UTC", "timeouts": null, "version_date": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_identity_dynamic_group.oke_nodes_dynamic_group", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1.module.postgresql", "mode": "managed", "type": "null_resource", "name": "ansible_provisioner", "provider": "provider[\"registry.terraform.io/hashicorp/null\"]", "instances": [{"schema_version": 0, "attributes": {"id": "8333640292303261542", "triggers": {"playbook_sha1": "5ab0a098e3636d855b06e96cfb264f826f3cc985", "vars_sha1": "c89263aa1d020078e874a203b830ad77b0a1a6e2"}}, "sensitive_attributes": [], "dependencies": ["module.cluster-1.module.postgresql.oci_core_instance.postgresql_vm", "module.cluster-1.module.postgresql.oci_core_route_table.postgres_route_table", "module.cluster-1.module.postgresql.oci_core_security_list.postgres_security_list", "module.cluster-1.module.postgresql.oci_core_subnet.private_subnet", "module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1.module.postgresql", "mode": "managed", "type": "oci_core_instance", "name": "postgresql_vm", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"agent_config": [{"are_all_plugins_disabled": false, "is_management_disabled": false, "is_monitoring_disabled": false, "plugins_config": []}], "async": null, "availability_config": [{"is_live_migration_preferred": false, "recovery_action": "RESTORE_INSTANCE"}], "availability_domain": "Ixbb:SA-VINHEDO-1-AD-1", "boot_volume_id": "ocid1.bootvolume.oc1.sa-vinhedo-1.ab3ggljrtr4fib2hbugztfvlenyw3sf66mvleihdqozssxjlihqbh3xzlgja", "capacity_reservation_id": null, "cluster_placement_group_id": null, "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "compute_cluster_id": null, "create_vnic_details": [{"assign_ipv6ip": false, "assign_private_dns_record": false, "assign_public_ip": "false", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-04T00:37:12.032Z"}, "display_name": "digisac-cluster-1-postgres", "freeform_tags": {}, "hostname_label": "digisac-cluster-1-postgres", "ipv6address_ipv6subnet_cidr_pair_details": [], "nsg_ids": [], "private_ip": "***********", "security_attributes": {}, "skip_source_dest_check": false, "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa7h26fb4o7rmjadaio7zm6mkkhtxs4obp3qpmlekuvelqtwprczfa", "vlan_id": ""}], "dedicated_vm_host_id": null, "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-04T00:37:11.896Z"}, "display_name": "digisac-cluster-1-postgres", "extended_metadata": {}, "fault_domain": "FAULT-DOMAIN-3", "freeform_tags": {}, "hostname_label": "digisac-cluster-1-postgres", "id": "ocid1.instance.oc1.sa-vinhedo-1.an3ggljrsfii6fichdvkvo6n7qz6veejjkhn55unjzdtymhhyhya4qni4yja", "image": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa26nbjofrqahy4b47mjegokq5rut6k3l7g2mzbsxxhw7bvbydbx6a", "instance_configuration_id": null, "instance_options": [{"are_legacy_imds_endpoints_disabled": false}], "ipxe_script": null, "is_cross_numa_node": false, "is_pv_encryption_in_transit_enabled": null, "launch_mode": "PARAVIRTUALIZED", "launch_options": [{"boot_volume_type": "PARAVIRTUALIZED", "firmware": "UEFI_64", "is_consistent_volume_naming_enabled": true, "is_pv_encryption_in_transit_enabled": false, "network_type": "PARAVIRTUALIZED", "remote_data_volume_type": "PARAVIRTUALIZED"}], "launch_volume_attachments": [], "licensing_configs": [], "metadata": {"ssh_authorized_keys": "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCwr8NrCr9AZbeE/BGdPF+h2kJNcVanHV8M1pYQrYX5xWRfjKkDXXFHme3y2nEexsdYcwDdcGi6qErDbIgsZgBGdLsi6g2ryUhmH1kfXoVJVqYBydA3xbNO4/hhLZK/3FQexANuZYjHOcTRbevp47yIE7nYKC6iu/rFo7GPIoUTrMQ4abNnIgGWW2Xud4YYQq8MQHUIsXQW2gcGGfH6xRkyt5O/zB1OO9w4XKderDvICMtJZ32+Ov0L/wrtSgFF0JyzxQBumwYUtgeHjfRYOWt3YpOY8GfTDJNg8hehbLN8UBpGIkYEoBI9SBEG+bfnf89P7zOSZWQegRiEZ3fzUGC/\n"}, "platform_config": [], "preemptible_instance_config": [], "preserve_boot_volume": null, "preserve_data_volumes_created_at_launch": null, "private_ip": "***********", "public_ip": "", "region": "sa-vinhedo-1", "security_attributes": {}, "security_attributes_state": "STABLE", "shape": "VM.Standard.E4.Flex", "shape_config": [{"baseline_ocpu_utilization": "", "gpu_description": "", "gpus": 0, "local_disk_description": "", "local_disks": 0, "local_disks_total_size_in_gbs": 0, "max_vnic_attachments": 2, "memory_in_gbs": 8, "networking_bandwidth_in_gbps": 2, "nvmes": 0, "ocpus": 2, "processor_description": "2.55 GHz AMD EPYC™ 7J13 (Milan)", "vcpus": 4}], "source_details": [{"boot_volume_size_in_gbs": "47", "boot_volume_vpus_per_gb": "10", "instance_source_image_filter_details": [], "is_preserve_boot_volume_enabled": false, "kms_key_id": "", "source_id": "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa26nbjofrqahy4b47mjegokq5rut6k3l7g2mzbsxxhw7bvbydbx6a", "source_type": "image"}], "state": "RUNNING", "subnet_id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa7h26fb4o7rmjadaio7zm6mkkhtxs4obp3qpmlekuvelqtwprczfa", "system_tags": {}, "time_created": "2025-03-04 00:37:12.392 +0000 UTC", "time_maintenance_reboot_due": "", "timeouts": null, "update_operation_constraint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoyNzAwMDAwMDAwMDAwLCJkZWxldGUiOjQ1MDAwMDAwMDAwMDAsInVwZGF0ZSI6MjcwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.module.postgresql.oci_core_route_table.postgres_route_table", "module.cluster-1.module.postgresql.oci_core_security_list.postgres_security_list", "module.cluster-1.module.postgresql.oci_core_subnet.private_subnet", "module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1.module.postgresql", "mode": "managed", "type": "oci_core_route_table", "name": "postgres_route_table", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-03T20:29:55.603Z"}, "display_name": "digisac-cluster-1-postgres-route-table", "freeform_tags": {}, "id": "ocid1.routetable.oc1.sa-vinhedo-1.aaaaaaaah3wua4lho6uezaljgj7pacfhgdmm5ufphrlmtmr375yauu3x5bca", "route_rules": [{"cidr_block": "", "description": "Internet Gateway", "destination": "0.0.0.0/0", "destination_type": "CIDR_BLOCK", "network_entity_id": "ocid1.natgateway.oc1.sa-vinhedo-1.aaaaaaaa2tbqovfirumqgeowszpllaaxua55w3pvqru3t4ttgubavrpxrjuq", "route_type": ""}, {"cidr_block": "", "description": "Peering Digisac-Cluster X Tools", "destination": "*********/16", "destination_type": "CIDR_BLOCK", "network_entity_id": "ocid1.localpeeringgateway.oc1.sa-vinhedo-1.aaaaaaaa62kdqmghj2ozbhnqwe36enb2z3g7kixaanmwe53xj3nn4uxghxvq", "route_type": ""}], "state": "AVAILABLE", "time_created": "2025-03-03 20:29:55.628 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1.module.postgresql", "mode": "managed", "type": "oci_core_security_list", "name": "postgres_security_list", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-03T20:29:55.490Z"}, "display_name": "digisac-cluster-1-postgres-security-list", "egress_security_rules": [{"description": "Allow all outbound traffic", "destination": "0.0.0.0/0", "destination_type": "CIDR_BLOCK", "icmp_options": [], "protocol": "all", "stateless": false, "tcp_options": [], "udp_options": []}], "freeform_tags": {}, "id": "ocid1.securitylist.oc1.sa-vinhedo-1.aaaaaaaamt5urujjkytbzrobe46ckxtuwrhbpybuep7co4mapivetmiw6ztq", "ingress_security_rules": [{"description": "Allow Postgres 5432 from VPN", "icmp_options": [], "protocol": "6", "source": "***********/32", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 5432, "min": 5432, "source_port_range": []}], "udp_options": []}, {"description": "Allow SSH from VPN", "icmp_options": [], "protocol": "6", "source": "***********/32", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 22, "min": 22, "source_port_range": []}], "udp_options": []}, {"description": "Allow internal communication from whole VCN CIDR block.", "icmp_options": [], "protocol": "6", "source": "**********/16", "source_type": "CIDR_BLOCK", "stateless": false, "tcp_options": [{"max": 5432, "min": 5432, "source_port_range": []}], "udp_options": []}], "state": "AVAILABLE", "time_created": "2025-03-03 20:29:55.518 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.cluster-1.module.postgresql", "mode": "managed", "type": "oci_core_subnet", "name": "private_subnet", "provider": "module.cluster-1.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"availability_domain": null, "cidr_block": "10.180.8.0/22", "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-03T20:55:59.495Z"}, "dhcp_options_id": "ocid1.dhcpoptions.oc1.sa-vinhedo-1.aaaaaaaai7bmb5tcphgd3c4deo6qvly53zjbs6gl4aoariztdtrpyfpduxyq", "display_name": "digisac-cluster-1-postgres-private-subnet", "dns_label": "postgresprivsub", "freeform_tags": {}, "id": "ocid1.subnet.oc1.sa-vinhedo-1.aaaaaaaa7h26fb4o7rmjadaio7zm6mkkhtxs4obp3qpmlekuvelqtwprczfa", "ipv6cidr_block": null, "ipv6cidr_blocks": [], "ipv6virtual_router_ip": null, "prohibit_internet_ingress": true, "prohibit_public_ip_on_vnic": true, "route_table_id": "ocid1.routetable.oc1.sa-vinhedo-1.aaaaaaaah3wua4lho6uezaljgj7pacfhgdmm5ufphrlmtmr375yauu3x5bca", "security_list_ids": ["ocid1.securitylist.oc1.sa-vinhedo-1.aaaaaaaamt5urujjkytbzrobe46ckxtuwrhbpybuep7co4mapivetmiw6ztq"], "state": "AVAILABLE", "subnet_domain_name": "postgresprivsub.digisacvcn.oraclevcn.com", "time_created": "2025-03-03 20:55:59.536 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq", "virtual_router_ip": "**********", "virtual_router_mac": "00:00:17:56:EF:5B"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.cluster-1.module.postgresql.oci_core_route_table.postgres_route_table", "module.cluster-1.module.postgresql.oci_core_security_list.postgres_security_list", "module.cluster-1.oci_core_nat_gateway.digisac_nat", "module.global.oci_core_local_peering_gateway.lgp_digisacCluster_tools", "module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.global", "mode": "managed", "type": "local_file", "name": "ocir_values", "provider": "provider[\"registry.terraform.io/hashicorp/local\"]", "instances": [{"schema_version": 0, "attributes": {"content": "\"authToken\": \"i5URua;iXf}kV]}<<]p[\"\n\"namespace\": \"app\"\n\"region\": \"sa-vinhedo-1\"\n\"secretName\": \"ocirsecret\"\n\"tenancyNamespace\": \"axvaplbwrlcl\"\n\"username\": \"ocir-pull-user\"\n", "content_base64": null, "content_base64sha256": "2PPo4fA5Ja9DADxsgzEUa2KYOab2u22lPGwvKEL4GSA=", "content_base64sha512": "s1VOvBKvIGRXFce+xjFkGOHaPrHniH4RgCUxcVZMRyXYztL/yutj/1xuDZGci5w6Jo9dUo4jJeiihJAb6ARXFg==", "content_md5": "f7e21d0c1dcef3a04a52b7c4aa0e6dd3", "content_sha1": "f31e0bb339997cab78a5b2648458ea9b7e7a696b", "content_sha256": "d8f3e8e1f03925af43003c6c8331146b629839a6f6bb6da53c6c2f2842f81920", "content_sha512": "b3554ebc12af20645715c7bec6316418e1da3eb1e7887e1180253171564c4725d8ced2ffcaeb63ff5c6e0d919c8b9c3a268f5d528e2325e8a284901be8045716", "directory_permission": "0777", "file_permission": "0644", "filename": "../kubernetes/tools/ocir-credentials/values.yaml", "id": "f31e0bb339997cab78a5b2648458ea9b7e7a696b", "sensitive_content": null, "source": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "sensitive_content"}]], "dependencies": ["module.global.oci_identity_auth_token.ocir_auth_token", "module.global.oci_identity_user.ocir_pull_user"]}]}, {"module": "module.global", "mode": "managed", "type": "oci_core_local_peering_gateway", "name": "lgp_digisacCluster_tools", "provider": "module.global.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-03T15:04:12.267Z"}, "display_name": "lgp-digisacCluster-tools", "freeform_tags": {}, "id": "ocid1.localpeeringgateway.oc1.sa-vinhedo-1.aaaaaaaa62kdqmghj2ozbhnqwe36enb2z3g7kixaanmwe53xj3nn4uxghxvq", "is_cross_tenancy_peering": false, "peer_advertised_cidr": "*********/16", "peer_advertised_cidr_details": ["*********/16"], "peer_id": "ocid1.localpeeringgateway.oc1.sa-vinhedo-1.aaaaaaaanvhypownyi7x4dwlvn5ufxttzn5mjxdtntt4wviiqw724pl6w3oq", "peering_status": "PEERED", "peering_status_details": "Connected to a peer.", "route_table_id": null, "state": "AVAILABLE", "time_created": "2025-03-03 15:04:12.31 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMCJ9", "dependencies": ["module.global.oci_core_vcn.digisac_vcn", "module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.global", "mode": "managed", "type": "oci_core_vcn", "name": "digisac_vcn", "provider": "module.global.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"byoipv6cidr_blocks": [], "byoipv6cidr_details": [], "cidr_block": "**********/16", "cidr_blocks": ["**********/16"], "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "default_dhcp_options_id": "ocid1.dhcpoptions.oc1.sa-vinhedo-1.aaaaaaaai7bmb5tcphgd3c4deo6qvly53zjbs6gl4aoariztdtrpyfpduxyq", "default_route_table_id": "ocid1.routetable.oc1.sa-vinhedo-1.aaaaaaaastoflybsoxmyy5edypusn3lmiwmf2mxg3ayhc3ddmsuangerlema", "default_security_list_id": "ocid1.securitylist.oc1.sa-vinhedo-1.aaaaaaaawodawcc2ofm6e6jzqyivi2e77bsvjv67mar46tzldyjnliezxhda", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:33.002Z"}, "display_name": "vcn-digisac-cluster", "dns_label": "digisacvcn", "freeform_tags": {}, "id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq", "ipv6cidr_blocks": [], "ipv6private_cidr_blocks": [], "is_ipv6enabled": false, "is_oracle_gua_allocation_enabled": null, "security_attributes": {}, "state": "AVAILABLE", "time_created": "2025-02-28 00:22:33.061 +0000 UTC", "timeouts": null, "vcn_domain_name": "digisacvcn.oraclevcn.com"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_identity_compartment.digisac"]}]}, {"module": "module.global", "mode": "managed", "type": "oci_identity_auth_token", "name": "ocir_auth_token", "provider": "module.global.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Auth token for OCIR access", "id": "ocid1.credential.oc1..aaaaaaaajnu6sb4kop45mv2fcpem66bgmnk7367lz3ssqwz7iyb6knvmlnla", "inactive_state": null, "state": "ACTIVE", "time_created": "2025-03-07 00:19:27.035 +0000 UTC", "time_expires": null, "timeouts": null, "token": "i5URua;iXf}kV]}<<]p[", "user_id": "ocid1.user.oc1..aaaaaaaa7pj2sqeviiraenjohnf2zwj27dtmj56ubgapbfefcdpsklso7jiq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_identity_user.ocir_pull_user"]}]}, {"module": "module.global", "mode": "managed", "type": "oci_identity_compartment", "name": "digisac", "provider": "module.global.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-02-28T00:22:13.096Z"}, "description": "Compartimento para o projeto Digisac kubernetes", "enable_delete": true, "freeform_tags": {}, "id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "inactive_state": null, "is_accessible": true, "name": "Digisac-Cluster", "state": "ACTIVE", "time_created": "2025-02-28 00:22:13.15 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjo1NDAwMDAwMDAwMDAwfX0="}]}, {"module": "module.global", "mode": "managed", "type": "oci_identity_group", "name": "ocir_pull_group", "provider": "module.global.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-07T00:19:26.220Z"}, "description": "Group for users with permissions to pull images from Oracle Container Registry", "freeform_tags": {}, "id": "ocid1.group.oc1..aaaaaaaabdaa6xeu7lbg27hoisrjk7dvm675miuyugrhhgpj42breofmkl5q", "inactive_state": null, "name": "ocir-pull-group", "state": "ACTIVE", "time_created": "2025-03-07 00:19:26.245 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"module": "module.global", "mode": "managed", "type": "oci_identity_policy", "name": "ocir_pull_policy", "provider": "module.global.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"ETag": "6ea85e727ce40bb565102bc820f6ed691ddb3645", "compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-07T00:20:02.847Z"}, "description": "Policy to allow pulling images from Oracle Container Registry", "freeform_tags": {}, "id": "ocid1.policy.oc1..aaaaaaaa3z7m5ayeg3s3lvyh63a7cvs6cda5ktlwttpes5iu3uxjzmgjdiia", "inactive_state": null, "lastUpdateETag": "6ea85e727ce40bb565102bc820f6ed691ddb3645", "name": "ocir-pull-policy", "policyHash": "be0204cb4c60b71d2b5d3d787e9eb1a0", "state": "ACTIVE", "statements": ["Allow group ocir-pull-group to inspect repos in compartment id ocid1.compartment.oc1..aaaaaaaadnapi3elokblvktxtq52kryjjqjhuk5zeqj6ojmz5pecblc3lrka", "Allow group ocir-pull-group to read repos in compartment id ocid1.compartment.oc1..aaaaaaaadnapi3elokblvktxtq52kryjjqjhuk5zeqj6ojmz5pecblc3lrka"], "time_created": "2025-03-07 00:20:02.862 +0000 UTC", "timeouts": null, "version_date": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_identity_group.ocir_pull_group"]}]}, {"module": "module.global", "mode": "managed", "type": "oci_identity_user", "name": "ocir_pull_user", "provider": "module.global.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"capabilities": [{"can_use_api_keys": true, "can_use_auth_tokens": true, "can_use_console_password": true, "can_use_customer_secret_keys": true, "can_use_db_credentials": true, "can_use_oauth2client_credentials": true, "can_use_smtp_credentials": true}], "compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "db_user_name": null, "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-07T00:19:26.409Z"}, "description": "User for pulling images from Oracle Container Registry", "email": null, "email_verified": false, "external_identifier": "83c8b4691c6b40bf8813f3a3d9fc6bd9", "freeform_tags": {}, "id": "ocid1.user.oc1..aaaaaaaa7pj2sqeviiraenjohnf2zwj27dtmj56ubgapbfefcdpsklso7jiq", "identity_provider_id": null, "inactive_state": null, "last_successful_login_time": null, "name": "ocir-pull-user", "previous_successful_login_time": null, "state": "ACTIVE", "time_created": "2025-03-07 00:19:26.442 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"module": "module.global", "mode": "managed", "type": "oci_identity_user_group_membership", "name": "ocir_user_group_membership", "provider": "module.global.provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.tenancy.oc1..aaaaaaaavxvnonq4xowa4dlly776u3uv76mkqh2fqyusdbhe3xmikvqjdysa", "group_id": "ocid1.group.oc1..aaaaaaaabdaa6xeu7lbg27hoisrjk7dvm675miuyugrhhgpj42breofmkl5q", "id": "ocid1.groupmembership.oc1..aaaaaaaadncwopxmwssuqpj3nq6tstbdlsto2j45rnrumx2ydhwpzydlkc6a", "inactive_state": null, "state": "ACTIVE", "time_created": "2025-03-07 00:19:29.103 +0000 UTC", "timeouts": null, "user_id": "ocid1.user.oc1..aaaaaaaa7pj2sqeviiraenjohnf2zwj27dtmj56ubgapbfefcdpsklso7jiq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.global.oci_identity_group.ocir_pull_group", "module.global.oci_identity_user.ocir_pull_user"]}]}], "check_results": null}