variable "compartment_name" {
  description = "Nome do compartimento"
  type        = string
  default     = "Digisac-Cluster"
}

variable "vcn_name" {
  description = "Nome da VCN"
  type        = string
  default     = "vcn-digisac-cluster"
}

variable "vcn_cidr" {
  description = "CIDR block para a VCN"
  type        = string
  default     = "**********/16"
}

variable "region" {
  description = "Região da OCI onde o cluster será criado"
  type        = string
  default     = "sa-vinhedo-1"
}

variable "tenancy_namespace" {
  type        = string
  description = "OCI tenancy namespace for OCIR"
}

variable "tenancy_ocid" {
  description = "OCID do tenancy"
  type        = string
}

variable "container_images_compartment_ocid" {
  description = "OCID do compartmento contendo imagens docker"
  type        = string
}

variable "user_ocid" {
  description = "OCID do usuário"
  type        = string
}

variable "fingerprint" {
  description = "Fingerprint da chave API"
  type        = string
}

variable "private_key_path" {
  description = "Caminho para a chave privada da API"
  type        = string
}