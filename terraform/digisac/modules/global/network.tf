# VCN
resource "oci_core_vcn" "digisac_vcn" {
  compartment_id = oci_identity_compartment.digisac.id
  cidr_blocks = [var.vcn_cidr]
  display_name   = var.vcn_name
  dns_label      = "digisacvcn"
}

# Local Peering Gateway
resource "oci_core_local_peering_gateway" "lgp_digisacCluster_tools" {
  compartment_id = oci_identity_compartment.digisac.id
  vcn_id         = oci_core_vcn.digisac_vcn.id
  display_name   = "lgp-digisacCluster-tools"
}

output "vcn_id" {
  value = oci_core_vcn.digisac_vcn.id
}

output "local_peering_gateway_id" {
  value = oci_core_local_peering_gateway.lgp_digisacCluster_tools.id
}