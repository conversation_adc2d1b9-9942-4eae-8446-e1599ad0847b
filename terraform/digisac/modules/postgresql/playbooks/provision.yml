---
- name: Configure PostgreSQL
  hosts: all
  become: yes
  vars:
    postgres_user: "{{ postgres_user }}"
    postgres_db: "{{ postgres_db }}"
    postgres_password: "{{ postgres_password }}"
    vcn_cidr: "{{ vcn_cidr }}"

  tasks:
    - name: Update YUM packages
      yum:
        name: '*'
        state: latest
        update_only: yes

    - name: Install psycopg2
      package:
        name: python3-psycopg2
        state: present
      become: yes

    - name: Install PostgreSQL packages
      yum:
        name:
          - postgresql-server
          - postgresql-contrib
        state: present

    - name: Check if PostgreSQL is initialized
      stat:
        path: /var/lib/pgsql/data/postgresql.conf
      register: postgres_data

    - name: Initialize PostgreSQL database
      command: postgresql-setup initdb
      when: not postgres_data.stat.exists

    - name: Start and enable PostgreSQL
      systemd:
        name: postgresql
        state: started
        enabled: yes

    - name: Backup original postgresql.conf
      copy:
        remote_src: yes
        src: /var/lib/pgsql/data/postgresql.conf
        dest: /var/lib/pgsql/data/postgresql.conf.bak
        owner: postgres
        group: postgres
        force: no

    - name: Configure postgresql.conf to listen on all addresses
      lineinfile:
        path: /var/lib/pgsql/data/postgresql.conf
        regexp: "^#listen_addresses.*"
        line: "listen_addresses = '*'"
        owner: postgres
        group: postgres
      notify: restart postgresql

    - name: Configure max_connections in postgresql.conf
      lineinfile:
        path: /var/lib/pgsql/data/postgresql.conf
        regexp: "^max_connections\\s*="
        line: "max_connections = 2000"
        owner: postgres
        group: postgres
      notify: restart postgresql

    - name: Backup original pg_hba.conf
      copy:
        remote_src: yes
        src: /var/lib/pgsql/data/pg_hba.conf
        dest: /var/lib/pgsql/data/pg_hba.conf.bak
        owner: postgres
        group: postgres
        force: no

    - name: Configure pg_hba.conf with template
      template:
        src: pg_hba.conf.j2
        dest: /var/lib/pgsql/data/pg_hba.conf
        owner: postgres
        group: postgres
        mode: '0600'
      notify: restart postgresql

    - name: Force handlers to run now
      meta: flush_handlers

    # Set postgres user password using peer authentication
    - name: Set postgres user password
      become: yes
      become_user: postgres
      postgresql_user:
        name: postgres
        password: "{{ postgres_password }}"
        state: present
        login_unix_socket: ""  # Use local Unix socket with peer auth
      when: postgres_user == 'postgres'

    # Create a non-postgres user if specified
    - name: Create non-postgres PostgreSQL user
      become: yes
      become_user: postgres
      postgresql_user:
        name: "{{ postgres_user }}"
        password: "{{ postgres_password }}"
        state: present
      when: postgres_user != 'postgres'

    - name: Create PostgreSQL database
      become_user: postgres
      postgresql_db:
        name: "{{ postgres_db }}"
        owner: "{{ postgres_user }}"
        state: present

    - name: Grant privileges to user
      become: yes
      become_user: postgres
      postgresql_privs:
        db: "{{ postgres_db }}"
        role: "{{ postgres_user }}"
        type: database
        privs: ALL
        state: present
        login_host: 127.0.0.1
        login_user: postgres
        login_password: "{{ postgres_password }}"
      when: postgres_user != 'postgres'

    - name: Validate PostgreSQL connection
      become: yes
      become_user: postgres
      postgresql_ping:
        db: "{{ postgres_db }}"
        login_user: "{{ postgres_user }}"
        login_password: "{{ postgres_password }}"
        login_host: 127.0.0.1
      register: result
      failed_when: not result.is_available

    - name: Ensure firewalld allows PostgreSQL connections
      firewalld:
        port: 5432/tcp
        permanent: yes
        state: enabled
      notify: restart firewalld

  handlers:
    - name: restart postgresql
      systemd:
        name: postgresql
        state: restarted

    - name: restart firewalld
      systemd:
        name: firewalld
        state: restarted