variable "private_subnet_cidr" {
  description = "CIDR block for the private subnet used by PostgreSQL."
  type        = string
}

variable "compartment_id" {
  description = "OCID of the compartment where resources will be created."
  type        = string
}

variable "nat_gateway_id" {
  description = "The OCID of the NAT Gateway for egress internet access."
  type        = string
}

variable "vcn_id" {
  description = "The OCID of the VCN in which the PostgreSQL resources will be deployed."
  type        = string
}

variable "availability_domain" {
  description = "The availability domain where the VM will be deployed."
  type        = string
}

variable "image_id" {
  description = "OCID of the image for the PostgreSQL VM (e.g., Oracle Linux)."
  type        = string
}

variable "vm_ocpus" {
  description = "Number of OCPUs for the PostgreSQL VM."
  type        = number
  default     = 2
}

variable "vm_memory_in_gbs" {
  description = "Memory in GBs for the PostgreSQL VM."
  type        = number
  default     = 8
}

variable "postgres_name" {
  description = "Name (display name) for the PostgreSQL VM."
  type        = string
}

variable "postgres_playbook_run" {
  description = "Run Postgres Stup Playbook."
  type        = bool
  default = false
}

variable "postgres_user" {
  description = "PostgreSQL user to create."
  type        = string
}

variable "postgres_password" {
  description = "Password for the PostgreSQL user."
  type        = string
}

variable "postgres_db" {
  description = "Name of the PostgreSQL database to create."
  type        = string
}

variable "ssh_private_key_path" {
  description = "The path to the private SSH key for connecting to the VM."
  type        = string
}

variable "ssh_public_key_path" {
  description = "Caminho para a chave publica"
  type        = string
}

variable "vcn_cidr" {
  description = "CIDR block for the VCN, used to allow internal access to PostgreSQL."
  type        = string
}

variable "vpn_cidr" {
  description = "CIDR block para liberação de acesso via VPN"
  type        = string
}

variable "local_peering_gateway_id" {
  description = "Local Peering Gateway ID"
  type        = string
}
