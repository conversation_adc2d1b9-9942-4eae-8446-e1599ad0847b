# OCI Dynamic Group para o Cluster Autoscaler
resource "oci_identity_dynamic_group" "oke_nodes_dynamic_group" {
  compartment_id = var.tenancy_ocid
  name           = "${var.cluster_name}-nodes-dg"
  description    = "Dynamic group for OKE nodes in cluster ${var.cluster_name}"
  matching_rule  = "ALL {instance.compartment.id = '${var.compartment_id}'}"
}

# Políticas IAM para autoscaler
resource "oci_identity_policy" "oke_autoscaler_policy" {
  compartment_id = var.tenancy_ocid
  name           = "${var.cluster_name}-autoscaler-policy"
  description    = "Policy to allow OKE nodes to manage node pools for autoscaling"

  statements = [
    "Allow dynamic-group ${oci_identity_dynamic_group.oke_nodes_dynamic_group.name} to manage cluster-node-pools in compartment id ${var.compartment_id}",
    "Allow dynamic-group ${oci_identity_dynamic_group.oke_nodes_dynamic_group.name} to manage instance-family in compartment id ${var.compartment_id}",
    "Allow dynamic-group ${oci_identity_dynamic_group.oke_nodes_dynamic_group.name} to use subnets in compartment id ${var.compartment_id}",
    "Allow dynamic-group ${oci_identity_dynamic_group.oke_nodes_dynamic_group.name} to read virtual-network-family in compartment id ${var.compartment_id}",
    "Allow dynamic-group ${oci_identity_dynamic_group.oke_nodes_dynamic_group.name} to use vnics in compartment id ${var.compartment_id}",
    "Allow dynamic-group ${oci_identity_dynamic_group.oke_nodes_dynamic_group.name} to inspect compartments in compartment id ${var.compartment_id}"
  ]
}

# Define node pools with their respective autoscaling settings
locals {
  node_pools = [
    {
      resource = oci_containerengine_node_pool.digisac_stateless_spot
      minNodes = 1
      maxNodes = 10
      priority = 10  # Highest priority (lowest number)
    },
    {
      resource = oci_containerengine_node_pool.digisac_stateless_fallback
      minNodes = 0
      maxNodes = 10
      priority = 100  # Lowest priority (highest number)
    },
    {
      resource = oci_containerengine_node_pool.digisac_statefull
      minNodes = 1
      maxNodes = 10
      priority = 50  # Medium priority
    },
    {
      resource = oci_containerengine_node_pool.digisac_tools
      minNodes = 1
      maxNodes = 5
      priority = 50  # Medium priority
    }
  ]

  # Transform the node pools into the exact structure needed for the YAML
  autoscaler_config = {
    pools = [
      for pool in local.node_pools : {
        name     = pool.resource.name
        id       = pool.resource.id
        minNodes = pool.minNodes
        maxNodes = pool.maxNodes
        priority = pool.priority
      }
    ]
    priorityExpander = {
      enabled = true
    }
  }
}

# Generate the YAML file using yamlencode
resource "local_file" "cluster_autoscaler_values" {
  content = yamlencode(local.autoscaler_config)
  filename = "../kubernetes/tools/oracle-cluster-autoscaler/${var.cluster_name}-values.yaml"
}