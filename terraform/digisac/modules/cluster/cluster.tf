# Cluster Kubernetes (OKE)
resource "oci_containerengine_cluster" "digisac_cluster" {
  compartment_id     = var.compartment_id
  kubernetes_version = var.kubernetes_version
  name               = var.cluster_name
  vcn_id             = var.vcn_id

  type = "ENHANCED_CLUSTER"

  # Configurações do cluster
  options {
    service_lb_subnet_ids = [oci_core_subnet.digisac_public_subnet.id]

    add_ons {
      is_kubernetes_dashboard_enabled = true
      is_tiller_enabled               = false
    }

    kubernetes_network_config {
      pods_cidr     = "**********/16"
      services_cidr = "*********/16"
    }
  }

  # Endpoint do cluster
  endpoint_config {
    subnet_id = oci_core_subnet.digisac_private_subnet.id
    is_public_ip_enabled = false
  }
}

# Network Security Group para o cluster
resource "oci_core_network_security_group" "cluster_nsg" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.cluster_name}-cluster-nsg"
}

# Regra para permitir acesso ao endpoint Kubernetes
resource "oci_core_network_security_group_security_rule" "cluster_nsg_rule" {
  network_security_group_id = oci_core_network_security_group.cluster_nsg.id
  direction                 = "INGRESS"
  protocol                  = "6" # TCP
  source                    = "0.0.0.0/0"
  source_type               = "CIDR_BLOCK"
  tcp_options {
    destination_port_range {
      min = 6443
      max = 6443
    }
  }
}