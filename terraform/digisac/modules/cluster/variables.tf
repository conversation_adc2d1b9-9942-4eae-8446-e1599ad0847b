variable "cluster_name" {
  description = "Nome do cluster Kubernetes"
  type        = string
}

variable "compartment_id" {
  description = "ID do compartimento"
  type        = string
}

variable "vcn_id" {
  description = "ID da VCN"
  type        = string
}

variable "vcn_cidr" {
  description = "CIDR block para a VCN"
  type        = string
}

variable "vpn_cidr" {
  description = "CIDR block para liberação de acesso via VPN"
  type        = string
}

variable "local_peering_gateway_id" {
  description = "Local Peering Gateway ID"
  type        = string
}

variable "public_subnet_cidr" {
  description = "CIDR block para a subnet pública"
  type        = string
}

variable "private_subnet_cidr" {
  description = "CIDR block for the private subnet"
  type        = string
}

variable "region" {
  description = "Região da OCI onde o cluster será criado"
  type        = string
  default     = "sa-vinhedo-1"
}

variable "tenancy_ocid" {
  description = "OCID do tenancy"
  type        = string
}

variable "user_ocid" {
  description = "OCID do usuário"
  type        = string
}

variable "fingerprint" {
  description = "Fingerprint da chave API"
  type        = string
}

variable "private_key_path" {
  description = "Caminho para a chave privada da API"
  type        = string
}

variable "ssh_public_key_path" {
  description = "Caminho para a chave publica"
  type        = string
}

variable "availability_domain" {
  description = "Availability Domain para os recursos"
  type        = string
  default     = "JKsD:SA-VINHEDO-1-AD-1"
}

variable "kubernetes_version" {
  description = "Versão do Kubernetes"
  type        = string
  default     = "v1.31.1"
}

variable "node_image_id" {
  description = "OCID da imagem para os nós do Kubernetes"
  type        = string
  default     = "ocid1.image.oc1.sa-saopaulo-1.aaaaaaaaqnr4qxivmay6gwrnupdvcwzlwhvgmzb3mrqy624efcy6nb5wxbua"
}

variable "postgres_playbook_run" {
  description = "Run Postgres Stup Playbook."
  type        = bool
  default = false
}

variable "postgres_private_subnet_cidr" {
  description = "CIDR block for the private subnet used by PostgreSQL"
  type        = string
}

variable "postgres_image_id" {
  description = "The image OCID for the PostgreSQL VM (e.g., Oracle Linux)."
  type        = string
  default     = "ocid1.image.oc1.sa-vinhedo-1.aaaaaaaa26nbjofrqahy4b47mjegokq5rut6k3l7g2mzbsxxhw7bvbydbx6a"
  // oracle linux 9.5 vinhedo
}

variable "postgres_vm_ocpus" {
  description = "Number of OCPUs for the PostgreSQL VM."
  type        = number
  default     = 2
}

variable "postgres_vm_memory_in_gbs" {
  description = "Memory in GBs for the PostgreSQL VM."
  type        = number
  default     = 8
}

variable "postgres_name" {
  description = "Display name of the PostgreSQL VM."
  type        = string
}

variable "postgres_user" {
  description = "PostgreSQL username to create."
  type        = string
  default     = "digisac"
}

variable "postgres_password" {
  description = "PostgreSQL password for the user."
  type        = string
  default     = "digisac"
}

variable "postgres_db" {
  description = "Name of the PostgreSQL database to create."
  type        = string
  default     = "digisac"
}

