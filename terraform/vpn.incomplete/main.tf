# Main Terraform configuration file
# Sets up providers for both Oracle Cloud and Tencent Cloud

# Define locals for sharing data between resources
locals {
  oracle_tunnel_ip = try(data.oci_core_ipsec_connection_tunnels.ipsec_tunnels.ip_sec_connection_tunnels[0].vpn_ip, "")
}

terraform {
  required_providers {
    oci = {
      source  = "oracle/oci"
      version = ">=4.87.0"
    }
    tencentcloud = {
      source  = "tencentcloudstack/tencentcloud"
      version = ">=1.77.0"
    }
  }
  required_version = ">= 0.14"
}

# Initialize Oracle Cloud provider
provider "oci" {
  tenancy_ocid     = var.oci_tenancy_ocid
  user_ocid        = var.oci_user_ocid
  fingerprint      = var.oci_fingerprint
  private_key_path = var.oci_private_key_path
  region           = var.oci_region
}

# Initialize Tencent Cloud provider
provider "tencentcloud" {
  secret_id  = var.tencent_secret_id
  secret_key = var.tencent_secret_key
  region     = var.tencent_region
}