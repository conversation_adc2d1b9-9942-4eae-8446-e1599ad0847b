# Example tfvars file - Copy to terraform.tfvars and fill in your values

# Oracle Cloud authentication
oci_tenancy_ocid     = "ocid1.tenancy.oc1..example"
oci_user_ocid        = "ocid1.user.oc1..example"
oci_fingerprint      = "aa:bb:cc:dd:ee:ff:gg:hh:ii:jj:kk:ll:mm:nn:oo:pp"
oci_private_key_path = "~/.oci/oci_api_key.pem"
oci_region           = "us-ashburn-1"

# Oracle Cloud resources
oci_compartment_id   = "ocid1.compartment.oc1..example"
oci_vcn_id           = "ocid1.vcn.oc1.iad.example"
oci_vcn_cidr         = "10.0.0.0/16"

# Tencent Cloud authentication
tencent_secret_id    = "AKIDxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
tencent_secret_key   = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
tencent_region       = "ap-singapore"

# Tencent Cloud resources
tencent_vpc_id       = "vpc-xxxxxxxx"
tencent_vpc_cidr     = "**********/16"
tencent_availability_zone = "ap-singapore-1"

# VPN configuration
ipsec_psk            = "YourVeryStrongPreSharedKey123!" # Make sure to use a strong key

# Optional: VPN protocol configuration (defaults provided in variables.tf)
# ike_version = "2"
# encryption_algorithm = "AES-256"
# authentication_algorithm = "SHA2-256"
# dh_group = "GROUP14"
# ike_lifetime = 28800
# ipsec_lifetime = 3600