output "tencent_route_table_id" {
  value       = tencentcloud_route_table.vpn_route_table.id
  description = "ID of the newly created route table"
}# Tencent Cloud VPN Configuration

# Create VPN Gateway
resource "tencentcloud_vpn_gateway" "vpn_gateway" {
  name                 = "oracle-vpn-gateway"
  vpc_id               = var.tencent_vpc_id
  bandwidth            = 100  # 100 Mbps
  type                 = "IPSEC"
  zone                 = var.tencent_availability_zone
  tags = {
    CreatedBy = "Terraform"
    Purpose   = "OracleVPN"
  }
}

# Create Customer Gateway (represents Oracle Cloud's VPN endpoint)
resource "tencentcloud_vpn_customer_gateway" "oracle_customer_gateway" {
  name              = "oracle-customer-gateway"
  # Use a placeholder IP initially and update it once Oracle endpoint is available
  public_ip_address = local.oracle_tunnel_ip != "" ? local.oracle_tunnel_ip : "0.0.0.0"
  tags = {
    CreatedBy = "Terraform"
    Purpose   = "OracleVPN"
  }

  depends_on = [data.oci_core_ipsec_connection_tunnels.ipsec_tunnels]

  # This lifecycle block prevents constant replacement when the IP is 0.0.0.0
  lifecycle {
    ignore_changes = [public_ip_address]
  }
}

# Create VPN Connection with simplified configuration based on provider capabilities
resource "tencentcloud_vpn_connection" "oracle_connection" {
  name                      = "oracle-vpn-connection"
  vpc_id                    = var.tencent_vpc_id
  vpn_gateway_id            = tencentcloud_vpn_gateway.vpn_gateway.id
  customer_gateway_id       = tencentcloud_vpn_customer_gateway.oracle_customer_gateway.id
  pre_share_key             = var.ipsec_psk

  # Remove the security group policy block entirely since it's causing issues
  # We'll rely on the route table entry instead

  # IKE configurations - using only supported parameters
  ike_version               = var.ike_version
  ike_proto_encry_algorithm = var.encryption_algorithm
  ike_exchange_mode         = "MAIN"
  ike_local_identity        = "ADDRESS"
  ike_sa_lifetime_seconds   = var.ike_lifetime
  ike_dh_group_name         = var.dh_group

  # IPSec configurations - using only supported parameters
  ipsec_encrypt_algorithm   = var.encryption_algorithm
  ipsec_sa_lifetime_seconds = var.ipsec_lifetime
  ipsec_pfs_dh_group        = var.dh_group

  tags = {
    CreatedBy = "Terraform"
    Purpose   = "OracleVPN"
  }
}

# Create a new route table for VPN traffic
resource "tencentcloud_route_table" "vpn_route_table" {
  vpc_id      = var.tencent_vpc_id
  name        = "oracle-vpn-route-table"
  tags = {
    CreatedBy = "Terraform"
    Purpose   = "OracleVPN"
  }
}

# Create route entry in the new route table to route traffic to Oracle Cloud through the VPN
resource "tencentcloud_route_table_entry" "route_to_oracle" {
  route_table_id         = tencentcloud_route_table.vpn_route_table.id
  destination_cidr_block = var.oci_vcn_cidr
  next_type              = "VPN"
  next_hub               = tencentcloud_vpn_gateway.vpn_gateway.id
}

# Outputs for Tencent Cloud resources
output "tencent_vpn_gateway_id" {
  value       = tencentcloud_vpn_gateway.vpn_gateway.id
  description = "ID of the Tencent Cloud VPN Gateway"
}

output "tencent_vpn_gateway_ip" {
  value       = tencentcloud_vpn_gateway.vpn_gateway.public_ip_address
  description = "Public IP address of the Tencent Cloud VPN Gateway"
}

output "tencent_vpn_connection_id" {
  value       = tencentcloud_vpn_connection.oracle_connection.id
  description = "ID of the Tencent Cloud VPN Connection"
}

output "tencent_vpn_connection_status" {
  value       = tencentcloud_vpn_connection.oracle_connection.state
  description = "Status of the Tencent Cloud VPN Connection"
}