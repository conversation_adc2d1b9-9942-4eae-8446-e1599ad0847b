# Oracle Cloud Infrastructure VPN Configuration

# Create Dynamic Routing Gateway (DRG)
resource "oci_core_drg" "oracle_drg" {
  compartment_id = var.oci_compartment_id
  display_name   = "tencent-oracle-drg"
}

# Attach DRG to VCN
resource "oci_core_drg_attachment" "drg_attachment" {
  drg_id       = oci_core_drg.oracle_drg.id
  vcn_id       = var.oci_vcn_id
  display_name = "tencent-oracle-drg-attachment"
}

# Create Customer-Premises Equipment (CPE) object for Tencent Cloud
resource "oci_core_cpe" "tencent_cpe" {
  compartment_id = var.oci_compartment_id
  display_name   = "tencent-cpe"
  ip_address     = tencentcloud_vpn_gateway.vpn_gateway.public_ip_address
}

# Create IPSec connection
resource "oci_core_ipsec" "oracle_tencent_ipsec" {
  compartment_id = var.oci_compartment_id
  cpe_id         = oci_core_cpe.tencent_cpe.id
  drg_id         = oci_core_drg.oracle_drg.id
  display_name   = "tencent-oracle-ipsec"
  static_routes  = [var.tencent_vpc_cidr]

  depends_on = [
    oci_core_drg_attachment.drg_attachment
  ]
}

# Get the created IPSec connection's tunnel information
data "oci_core_ipsec_connection_tunnels" "ipsec_tunnels" {
  ipsec_id = oci_core_ipsec.oracle_tencent_ipsec.id
}

# Create route table for traffic to Tencent Cloud
resource "oci_core_route_table" "route_to_tencent" {
  compartment_id = var.oci_compartment_id
  vcn_id         = var.oci_vcn_id
  display_name   = "route-to-tencent-cloud"

  route_rules {
    destination       = var.tencent_vpc_cidr
    destination_type  = "CIDR_BLOCK"
    network_entity_id = oci_core_drg.oracle_drg.id
  }
}

# Store tunnel information
resource "local_file" "tunnel_info" {
  content         = jsonencode(data.oci_core_ipsec_connection_tunnels.ipsec_tunnels.ip_sec_connection_tunnels)
  filename        = "${path.module}/tunnel_info.json"
  file_permission = "0644"
  depends_on      = [data.oci_core_ipsec_connection_tunnels.ipsec_tunnels]
}

# Local script to setup tunnel (executed during apply)
resource "null_resource" "setup_tunnel" {
  triggers = {
    ipsec_id = oci_core_ipsec.oracle_tencent_ipsec.id
  }

  provisioner "local-exec" {
    command = <<-EOT
      # Wait for tunnels to be created
      sleep 20

      # Get tunnel information
      oci network ipsec-connection-tunnel list --ipsec-id ${oci_core_ipsec.oracle_tencent_ipsec.id} > tunnel_list.json

      # Get first tunnel ID
      TUNNEL_ID=$(cat tunnel_list.json | jq -r '.data[0].id')

      # Update tunnel configuration
      if [ ! -z "$TUNNEL_ID" ]; then
        echo "Configuring tunnel: $TUNNEL_ID"
        oci network ipsec-connection-tunnel update \
          --tunnel-id $TUNNEL_ID \
          --routing STATIC \
          --ike-version ${var.ike_version} \
          --shared-secret ${var.ipsec_psk}
      else
        echo "No tunnel found to configure"
        exit 1
      fi
    EOT
  }

  depends_on = [
    oci_core_ipsec.oracle_tencent_ipsec,
    data.oci_core_ipsec_connection_tunnels.ipsec_tunnels,
    local_file.tunnel_info
  ]
}

# Outputs for Oracle Cloud resources
output "oracle_drg_id" {
  value       = oci_core_drg.oracle_drg.id
  description = "OCID of the Dynamic Routing Gateway"
}

output "oracle_tunnel_details" {
  value = [for tunnel in data.oci_core_ipsec_connection_tunnels.ipsec_tunnels.ip_sec_connection_tunnels : {
    id     = tunnel.id
    status = tunnel.status
    ip     = tunnel.vpn_ip
  }]
  description = "Details of the IPSec tunnels"
}

output "oracle_tunnel_ip" {
  value       = try(data.oci_core_ipsec_connection_tunnels.ipsec_tunnels.ip_sec_connection_tunnels[0].vpn_ip, "")
  description = "IP address of the Oracle tunnel endpoint"
}