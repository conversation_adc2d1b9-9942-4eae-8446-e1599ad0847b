# Oracle Cloud and Tencent Cloud VPN Interconnection

This Terraform configuration sets up a site-to-site VPN connection between Oracle Cloud Infrastructure (OCI) and Tencent Cloud. It creates all necessary resources on both cloud platforms to establish secure IPSec tunnels between the two environments.

## Prerequisites

- [Terraform](https://www.terraform.io/downloads.html) (v0.14 or later)
- Oracle Cloud Infrastructure account with necessary permissions
- Tencent Cloud account with necessary permissions
- API credentials for both cloud providers

## File Structure

```
.
├── main.tf                 # Provider configuration
├── variables.tf            # Variable definitions for both clouds
├── oracle.tf               # Oracle Cloud VPN resources
├── tencent.tf              # Tencent Cloud VPN resources
├── terraform.tfvars        # Variable values (create from example)
└── README.md               # This file
```

## Setup Instructions

1. **Clone or download this repository**

2. **Create your `terraform.tfvars` file**
   
   Copy the example file and fill in your values:
   ```
   cp terraform.tfvars.example terraform.tfvars
   ```
   
   Edit `terraform.tfvars` with your cloud provider credentials and resource IDs.

3. **Initialize Terraform**
   
   ```
   terraform init
   ```

4. **Review the execution plan**
   
   ```
   terraform plan
   ```

5. **Apply the configuration**
   
   ```
   terraform apply
   ```

6. **Verify the connection**
   
   After deployment completes, check the status of the VPN connections in both cloud consoles.

## Resource Details

### Oracle Cloud Resources

- **Dynamic Routing Gateway (DRG)**: Acts as the virtual router for the VPN connection
- **DRG Attachment**: Connects the DRG to your VCN
- **Customer-Premises Equipment (CPE)**: Represents the Tencent Cloud VPN gateway
- **IPSec Connection**: Establishes the secure tunnel to Tencent Cloud
- **Route Table**: Routes traffic destined for Tencent Cloud VPC through the DRG

### Tencent Cloud Resources

- **VPN Gateway**: The VPN endpoint in Tencent Cloud
- **Customer Gateway**: Represents the Oracle Cloud VPN endpoint
- **VPN Connection**: Establishes the secure tunnel to Oracle Cloud
- **Route Table Entry**: Routes traffic destined for Oracle Cloud VCN through the VPN gateway

## Important Notes

- **Dependencies**: The resources are created with proper dependencies to ensure the correct order of creation.
- **Encryption Settings**: By default, AES-256 encryption and SHA2-256 authentication are used with DH Group 14. These can be customized in the variables file.
- **Route Tables**: Make sure your subnets are associated with the route tables created or modified by this configuration.
- **Security Groups/Network ACLs**: You may need to configure security groups or network ACLs in both cloud environments to allow the necessary traffic.

## Troubleshooting

If the VPN connection is not establishing:

1. Check that the pre-shared key is identical on both sides
2. Verify that the encryption and authentication settings match
3. Ensure the security groups/network ACLs allow IPSec traffic (UDP 500, UDP 4500, ESP)
4. Check the route tables to ensure traffic is being directed to the VPN gateways

## Security Considerations

- Store your Terraform state file securely, as it contains sensitive information
- Consider using a secrets management solution for the pre-shared key
- Implement least privilege access control for the VPN connection