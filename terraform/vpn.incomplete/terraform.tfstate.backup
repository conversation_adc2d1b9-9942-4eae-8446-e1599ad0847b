{"version": 4, "terraform_version": "1.11.0", "serial": 13, "lineage": "285f0756-da90-299e-b8aa-f3b7ab1d0c1e", "outputs": {"oracle_drg_id": {"value": "ocid1.drg.oc1.sa-vinhedo-1.aaaaaaaa3qnvn2jmxgqgwb3kxo7fxakff4dohl3zb3ince24f7o6b2efyixq", "type": "string"}, "oracle_tunnel_details": {"value": [{"id": "ocid1.ipsectunnel.oc1.sa-vinhedo-1.aaaaaaaa6is4omonv3wqbth4mcomazd5qxl3k3m7xjukggdmlygmjwgzzfza", "ip": "***************", "status": "DOWN"}, {"id": "ocid1.ipsectunnel.oc1.sa-vinhedo-1.aaaaaaaahz2qatz4zxq775qpfhck4efmz7hnwfg2pjm3v3krofpetm5xwomq", "ip": "***************", "status": "DOWN"}], "type": ["tuple", [["object", {"id": "string", "ip": "string", "status": "string"}], ["object", {"id": "string", "ip": "string", "status": "string"}]]]}, "oracle_tunnel_ip": {"value": "***************", "type": "string"}, "tencent_route_table_id": {"value": "rtb-j6l3vub2", "type": "string"}, "tencent_vpn_gateway_id": {"value": "vpngw-1zoxqocu", "type": "string"}, "tencent_vpn_gateway_ip": {"value": "**************", "type": "string"}}, "resources": [{"mode": "data", "type": "oci_core_ipsec_connection_tunnels", "name": "ipsec_tunnels", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"filter": null, "id": "CoreIpSecConnectionTunnelsDataSource-**********", "ip_sec_connection_tunnels": [{"associated_virtual_circuits": [], "bgp_session_info": [], "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "cpe_ip": "**************", "display_name": "ipsectunnel20250318185417-2", "dpd_config": [], "dpd_mode": "INITIATE_AND_RESPOND", "dpd_timeout_in_sec": 20, "encryption_domain_config": [], "id": "ocid1.ipsectunnel.oc1.sa-vinhedo-1.aaaaaaaa6is4omonv3wqbth4mcomazd5qxl3k3m7xjukggdmlygmjwgzzfza", "ike_version": "V1", "ipsec_id": "", "nat_translation_enabled": "AUTO", "oracle_can_initiate": "INITIATOR_OR_RESPONDER", "phase_one_details": [{"custom_authentication_algorithm": "", "custom_dh_group": "", "custom_encryption_algorithm": "", "is_custom_phase_one_config": false, "is_ike_established": true, "lifetime": 28800, "negotiated_authentication_algorithm": "", "negotiated_dh_group": "", "negotiated_encryption_algorithm": "", "remaining_lifetime": "", "remaining_lifetime_int": 0, "remaining_lifetime_last_retrieved": "2025-03-18 18:56:21.527 +0000 UTC"}], "phase_two_details": [{"custom_authentication_algorithm": "", "custom_encryption_algorithm": "", "dh_group": "GROUP5", "is_custom_phase_two_config": false, "is_esp_established": false, "is_pfs_enabled": true, "lifetime": 3600, "negotiated_authentication_algorithm": "", "negotiated_dh_group": "", "negotiated_encryption_algorithm": "", "remaining_lifetime": "", "remaining_lifetime_int": 0, "remaining_lifetime_last_retrieved": "2025-03-18 18:56:21.527 +0000 UTC"}], "routing": "STATIC", "shared_secret": "", "state": "AVAILABLE", "status": "DOWN", "time_created": "2025-03-18 18:54:17.657 +0000 UTC", "time_status_updated": "2025-03-18 18:56:21.523 +0000 UTC", "tunnel_id": "", "vpn_ip": "***************"}, {"associated_virtual_circuits": [], "bgp_session_info": [], "compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "cpe_ip": "**************", "display_name": "ipsectunnel20250318185417-1", "dpd_config": [], "dpd_mode": "INITIATE_AND_RESPOND", "dpd_timeout_in_sec": 20, "encryption_domain_config": [], "id": "ocid1.ipsectunnel.oc1.sa-vinhedo-1.aaaaaaaahz2qatz4zxq775qpfhck4efmz7hnwfg2pjm3v3krofpetm5xwomq", "ike_version": "V1", "ipsec_id": "", "nat_translation_enabled": "AUTO", "oracle_can_initiate": "INITIATOR_OR_RESPONDER", "phase_one_details": [{"custom_authentication_algorithm": "", "custom_dh_group": "", "custom_encryption_algorithm": "", "is_custom_phase_one_config": false, "is_ike_established": true, "lifetime": 28800, "negotiated_authentication_algorithm": "", "negotiated_dh_group": "", "negotiated_encryption_algorithm": "", "remaining_lifetime": "", "remaining_lifetime_int": 0, "remaining_lifetime_last_retrieved": "2025-03-18 18:56:21.527 +0000 UTC"}], "phase_two_details": [{"custom_authentication_algorithm": "", "custom_encryption_algorithm": "", "dh_group": "GROUP5", "is_custom_phase_two_config": false, "is_esp_established": false, "is_pfs_enabled": true, "lifetime": 3600, "negotiated_authentication_algorithm": "", "negotiated_dh_group": "", "negotiated_encryption_algorithm": "", "remaining_lifetime": "", "remaining_lifetime_int": 0, "remaining_lifetime_last_retrieved": "2025-03-18 18:56:21.527 +0000 UTC"}], "routing": "STATIC", "shared_secret": "", "state": "AVAILABLE", "status": "DOWN", "time_created": "2025-03-18 18:54:17.64 +0000 UTC", "time_status_updated": "2025-03-18 18:56:21.523 +0000 UTC", "tunnel_id": "", "vpn_ip": "***************"}], "ipsec_id": "ocid1.ipsecconnection.oc1.sa-vinhedo-1.aaaaaaaavsi6nfmgygekg2xk247lchxh6sf6c2lpue4vnb4kxfehrab5zi5a"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "local_file", "name": "tunnel_info", "provider": "provider[\"registry.terraform.io/hashicorp/local\"]", "instances": [{"schema_version": 0, "attributes": {"content": "[{\"associated_virtual_circuits\":[],\"bgp_session_info\":[],\"compartment_id\":\"ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q\",\"cpe_ip\":\"**************\",\"display_name\":\"ipsectunnel20250318185417-2\",\"dpd_config\":[],\"dpd_mode\":\"INITIATE_AND_RESPOND\",\"dpd_timeout_in_sec\":20,\"encryption_domain_config\":[],\"id\":\"ocid1.ipsectunnel.oc1.sa-vinhedo-1.aaaaaaaa6is4omonv3wqbth4mcomazd5qxl3k3m7xjukggdmlygmjwgzzfza\",\"ike_version\":\"V1\",\"ipsec_id\":\"\",\"nat_translation_enabled\":\"AUTO\",\"oracle_can_initiate\":\"INITIATOR_OR_RESPONDER\",\"phase_one_details\":[{\"custom_authentication_algorithm\":\"\",\"custom_dh_group\":\"\",\"custom_encryption_algorithm\":\"\",\"is_custom_phase_one_config\":false,\"is_ike_established\":true,\"lifetime\":28800,\"negotiated_authentication_algorithm\":\"\",\"negotiated_dh_group\":\"\",\"negotiated_encryption_algorithm\":\"\",\"remaining_lifetime\":\"\",\"remaining_lifetime_int\":0,\"remaining_lifetime_last_retrieved\":\"2025-03-18 18:56:21.527 +0000 UTC\"}],\"phase_two_details\":[{\"custom_authentication_algorithm\":\"\",\"custom_encryption_algorithm\":\"\",\"dh_group\":\"GROUP5\",\"is_custom_phase_two_config\":false,\"is_esp_established\":false,\"is_pfs_enabled\":true,\"lifetime\":3600,\"negotiated_authentication_algorithm\":\"\",\"negotiated_dh_group\":\"\",\"negotiated_encryption_algorithm\":\"\",\"remaining_lifetime\":\"\",\"remaining_lifetime_int\":0,\"remaining_lifetime_last_retrieved\":\"2025-03-18 18:56:21.527 +0000 UTC\"}],\"routing\":\"STATIC\",\"shared_secret\":\"\",\"state\":\"AVAILABLE\",\"status\":\"DOWN\",\"time_created\":\"2025-03-18 18:54:17.657 +0000 UTC\",\"time_status_updated\":\"2025-03-18 18:56:21.523 +0000 UTC\",\"tunnel_id\":\"\",\"vpn_ip\":\"***************\"},{\"associated_virtual_circuits\":[],\"bgp_session_info\":[],\"compartment_id\":\"ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q\",\"cpe_ip\":\"**************\",\"display_name\":\"ipsectunnel20250318185417-1\",\"dpd_config\":[],\"dpd_mode\":\"INITIATE_AND_RESPOND\",\"dpd_timeout_in_sec\":20,\"encryption_domain_config\":[],\"id\":\"ocid1.ipsectunnel.oc1.sa-vinhedo-1.aaaaaaaahz2qatz4zxq775qpfhck4efmz7hnwfg2pjm3v3krofpetm5xwomq\",\"ike_version\":\"V1\",\"ipsec_id\":\"\",\"nat_translation_enabled\":\"AUTO\",\"oracle_can_initiate\":\"INITIATOR_OR_RESPONDER\",\"phase_one_details\":[{\"custom_authentication_algorithm\":\"\",\"custom_dh_group\":\"\",\"custom_encryption_algorithm\":\"\",\"is_custom_phase_one_config\":false,\"is_ike_established\":true,\"lifetime\":28800,\"negotiated_authentication_algorithm\":\"\",\"negotiated_dh_group\":\"\",\"negotiated_encryption_algorithm\":\"\",\"remaining_lifetime\":\"\",\"remaining_lifetime_int\":0,\"remaining_lifetime_last_retrieved\":\"2025-03-18 18:56:21.527 +0000 UTC\"}],\"phase_two_details\":[{\"custom_authentication_algorithm\":\"\",\"custom_encryption_algorithm\":\"\",\"dh_group\":\"GROUP5\",\"is_custom_phase_two_config\":false,\"is_esp_established\":false,\"is_pfs_enabled\":true,\"lifetime\":3600,\"negotiated_authentication_algorithm\":\"\",\"negotiated_dh_group\":\"\",\"negotiated_encryption_algorithm\":\"\",\"remaining_lifetime\":\"\",\"remaining_lifetime_int\":0,\"remaining_lifetime_last_retrieved\":\"2025-03-18 18:56:21.527 +0000 UTC\"}],\"routing\":\"STATIC\",\"shared_secret\":\"\",\"state\":\"AVAILABLE\",\"status\":\"DOWN\",\"time_created\":\"2025-03-18 18:54:17.64 +0000 UTC\",\"time_status_updated\":\"2025-03-18 18:56:21.523 +0000 UTC\",\"tunnel_id\":\"\",\"vpn_ip\":\"***************\"}]", "content_base64": null, "content_base64sha256": "52PpiDHb4ujAe6gkU46Y/jVOtYf2C25QmG8JQch7nag=", "content_base64sha512": "nwD/CdYamecA6Jm0G90EO4lYEe3J4xeKexMLvLM7Qm09iYezmo+nqxd5X31rO1bg5jPP7qQO4qk2eucbqaMpzQ==", "content_md5": "29c1d62420efc09b52cb74a06b7fe703", "content_sha1": "ee25d37e5bc623fed19d4231be3920ff8a079341", "content_sha256": "e763e98831dbe2e8c07ba824538e98fe354eb587f60b6e50986f0941c87b9da8", "content_sha512": "9f00ff09d61a99e700e899b41bdd043b895811edc9e3178a7b130bbcb33b426d3d8987b39a8fa7ab17795f7d6b3b56e0e633cfeea40ee2a9367ae71ba9a329cd", "directory_permission": "0777", "file_permission": "0644", "filename": "./tunnel_info.json", "id": "ee25d37e5bc623fed19d4231be3920ff8a079341", "sensitive_content": null, "source": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "sensitive_content"}]], "dependencies": ["data.oci_core_ipsec_connection_tunnels.ipsec_tunnels", "oci_core_cpe.tencent_cpe", "oci_core_drg.oracle_drg", "oci_core_drg_attachment.drg_attachment", "oci_core_ipsec.oracle_tencent_ipsec", "tencentcloud_vpn_gateway.vpn_gateway"]}]}, {"mode": "managed", "type": "null_resource", "name": "setup_tunnel", "provider": "provider[\"registry.terraform.io/hashicorp/null\"]", "instances": [{"status": "tainted", "schema_version": 0, "attributes": {"id": "5682221717198084121", "triggers": {"ipsec_id": "ocid1.ipsecconnection.oc1.sa-vinhedo-1.aaaaaaaavsi6nfmgygekg2xk247lchxh6sf6c2lpue4vnb4kxfehrab5zi5a"}}, "sensitive_attributes": [], "dependencies": ["data.oci_core_ipsec_connection_tunnels.ipsec_tunnels", "local_file.tunnel_info", "oci_core_cpe.tencent_cpe", "oci_core_drg.oracle_drg", "oci_core_drg_attachment.drg_attachment", "oci_core_ipsec.oracle_tencent_ipsec", "tencentcloud_vpn_gateway.vpn_gateway"]}]}, {"mode": "managed", "type": "oci_core_cpe", "name": "tencent_cpe", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "cpe_device_shape_id": "57e0f383-6887-4d4f-8093-8bff6b42cb1d", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-18T18:54:17.284Z"}, "display_name": "tencent-cpe", "freeform_tags": {}, "id": "ocid1.cpe.oc1.sa-vinhedo-1.aaaaaaaa5fowpewvkljemvz5u4xt2ylbprwajdqx6asx7i5iiwiqt67ktpea", "ip_address": "**************", "is_private": false, "time_created": "2025-03-18 18:54:17.306 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["tencentcloud_vpn_gateway.vpn_gateway"]}]}, {"mode": "managed", "type": "oci_core_drg", "name": "oracle_drg", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "default_drg_route_tables": [{"ipsec_tunnel": "ocid1.drgroutetable.oc1.sa-vinhedo-1.aaaaaaaavfab2khnvytvger6x4qflpyoa2uwzh7ie5tbckgyna2ux572mzeq", "remote_peering_connection": "ocid1.drgroutetable.oc1.sa-vinhedo-1.aaaaaaaavfab2khnvytvger6x4qflpyoa2uwzh7ie5tbckgyna2ux572mzeq", "vcn": "ocid1.drgroutetable.oc1.sa-vinhedo-1.aaaaaaaafrkxuigsv7oxqsh77tr57ilnaliyk74dcyp46tzwrlxhw6wtzndq", "virtual_circuit": "ocid1.drgroutetable.oc1.sa-vinhedo-1.aaaaaaaavfab2khnvytvger6x4qflpyoa2uwzh7ie5tbckgyna2ux572mzeq"}], "default_export_drg_route_distribution_id": "ocid1.drgroutedistribution.oc1.sa-vinhedo-1.aaaaaaaaswn5pgdqn3jzb7ivz5ifwiaogj43mqukiih4ve747v3zsu7yk62q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-18T18:52:36.093Z"}, "display_name": "tencent-oracle-drg", "freeform_tags": {}, "id": "ocid1.drg.oc1.sa-vinhedo-1.aaaaaaaa3qnvn2jmxgqgwb3kxo7fxakff4dohl3zb3ince24f7o6b2efyixq", "redundancy_status": "NOT_AVAILABLE", "state": "AVAILABLE", "time_created": "2025-03-18 18:52:36.108 +0000 UTC", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "oci_core_drg_attachment", "name": "drg_attachment", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {}, "display_name": "tencent-oracle-drg-attachment", "drg_id": "ocid1.drg.oc1.sa-vinhedo-1.aaaaaaaa3qnvn2jmxgqgwb3kxo7fxakff4dohl3zb3ince24f7o6b2efyixq", "drg_route_table_id": "ocid1.drgroutetable.oc1.sa-vinhedo-1.aaaaaaaafrkxuigsv7oxqsh77tr57ilnaliyk74dcyp46tzwrlxhw6wtzndq", "export_drg_route_distribution_id": null, "freeform_tags": {}, "id": "ocid1.drgattachment.oc1.sa-vinhedo-1.aaaaaaaavayrs7zbs42aiaz66bq5unpxmnrh7vmctxtazwa3ujfkcypjp35a", "is_cross_tenancy": false, "network_details": [{"id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq", "ids": [], "ipsec_connection_id": "", "route_table_id": "", "transport_attachment_id": "", "transport_only_mode": false, "type": "VCN", "vcn_route_type": "SUBNET_CIDRS"}], "remove_export_drg_route_distribution_trigger": null, "route_table_id": null, "state": "ATTACHED", "time_created": "2025-03-18 18:53:00.665 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["oci_core_drg.oracle_drg"]}]}, {"mode": "managed", "type": "oci_core_ipsec", "name": "oracle_tencent_ipsec", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "cpe_id": "ocid1.cpe.oc1.sa-vinhedo-1.aaaaaaaa5fowpewvkljemvz5u4xt2ylbprwajdqx6asx7i5iiwiqt67ktpea", "cpe_local_identifier": "**************", "cpe_local_identifier_type": "IP_ADDRESS", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-18T18:54:17.432Z"}, "display_name": "tencent-oracle-ipsec", "drg_id": "ocid1.drg.oc1.sa-vinhedo-1.aaaaaaaa3qnvn2jmxgqgwb3kxo7fxakff4dohl3zb3ince24f7o6b2efyixq", "freeform_tags": {}, "id": "ocid1.ipsecconnection.oc1.sa-vinhedo-1.aaaaaaaavsi6nfmgygekg2xk247lchxh6sf6c2lpue4vnb4kxfehrab5zi5a", "state": "AVAILABLE", "static_routes": ["10.0.0.0/16"], "time_created": "2025-03-18 18:54:17.507 +0000 UTC", "timeouts": null, "transport_type": "INTERNET", "tunnel_configuration": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["oci_core_cpe.tencent_cpe", "oci_core_drg.oracle_drg", "oci_core_drg_attachment.drg_attachment", "tencentcloud_vpn_gateway.vpn_gateway"]}]}, {"mode": "managed", "type": "oci_core_route_table", "name": "route_to_tencent", "provider": "provider[\"registry.terraform.io/oracle/oci\"]", "instances": [{"schema_version": 0, "attributes": {"compartment_id": "ocid1.compartment.oc1..aaaaaaaapxqouxnkmuf4c2w5v6i4yrrtmb7dws2phuok6tob3ur5hdanjt3q", "defined_tags": {"Oracle-Tags.CreatedBy": "oracleidentitycloudservice/<EMAIL>", "Oracle-Tags.CreatedOn": "2025-03-18T18:53:00.331Z"}, "display_name": "route-to-tencent-cloud", "freeform_tags": {}, "id": "ocid1.routetable.oc1.sa-vinhedo-1.aaaaaaaa2mcwsgxz3tf2icwzof6dnoyu23lgdfws26d2wg7t6c7ctsl3jvha", "route_rules": [{"cidr_block": "", "description": "", "destination": "10.0.0.0/16", "destination_type": "CIDR_BLOCK", "network_entity_id": "ocid1.drg.oc1.sa-vinhedo-1.aaaaaaaa3qnvn2jmxgqgwb3kxo7fxakff4dohl3zb3ince24f7o6b2efyixq", "route_type": ""}], "state": "AVAILABLE", "time_created": "2025-03-18 18:53:00.353 +0000 UTC", "timeouts": null, "vcn_id": "ocid1.vcn.oc1.sa-vinhedo-1.amaaaaaasfii6fiaj7zvktejqo3mgfe2soeksqje7yemehyrszwbjjiovleq"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["oci_core_drg.oracle_drg"]}]}, {"mode": "managed", "type": "tencentcloud_route_table", "name": "vpn_route_table", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"create_time": "2025-03-19 02:52:38", "id": "rtb-j6l3vub2", "is_default": false, "name": "oracle-vpn-route-table", "route_entry_ids": [], "subnet_ids": [], "tags": {"CreatedBy": "Terraform", "Purpose": "OracleVPN"}, "vpc_id": "vpc-kwnoutv1"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "tencentcloud_route_table_entry", "name": "route_to_oracle", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"description": "", "destination_cidr_block": "**********/16", "disabled": false, "id": "161768.rtb-j6l3vub2", "next_hub": "vpngw-1zoxqocu", "next_type": "VPN", "route_item_id": "rti-0nsd7flp", "route_table_id": "rtb-j6l3vub2"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_route_table.vpn_route_table", "tencentcloud_vpn_gateway.vpn_gateway"]}]}, {"mode": "managed", "type": "tencentcloud_vpn_gateway", "name": "vpn_gateway", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"bandwidth": 100, "bgp_asn": null, "cdc_id": "", "charge_type": "POSTPAID_BY_HOUR", "create_time": "2025-03-19 02:52:40", "expired_time": "2038-01-08 23:59:59", "id": "vpngw-1zoxqocu", "is_address_blocked": false, "max_connection": 25, "name": "oracle-vpn-gateway", "new_purchase_plan": "", "prepaid_period": 1, "prepaid_renew_flag": "NOTIFY_AND_AUTO_RENEW", "public_ip_address": "**************", "restrict_state": "NORMAL", "state": "AVAILABLE", "tags": {"CreatedBy": "Terraform", "Purpose": "OracleVPN"}, "type": "IPSEC", "vpc_id": "vpc-kwnoutv1", "zone": "sa-saopaulo-1"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}], "check_results": null}