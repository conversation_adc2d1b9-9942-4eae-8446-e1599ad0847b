# Variables for both Oracle Cloud and Tencent Cloud configurations

# Oracle Cloud Variables
variable "oci_tenancy_ocid" {
  description = "OCID of your Oracle Cloud tenancy"
  type        = string
}

variable "oci_user_ocid" {
  description = "OCID of the Oracle Cloud user"
  type        = string
}

variable "oci_fingerprint" {
  description = "Fingerprint of the Oracle Cloud API key"
  type        = string
}

variable "oci_private_key_path" {
  description = "Path to the Oracle Cloud private key file"
  type        = string
}

variable "oci_region" {
  description = "Oracle Cloud region"
  type        = string
}

variable "oci_compartment_id" {
  description = "OCID of the Oracle Cloud compartment"
  type        = string
}

variable "oci_vcn_id" {
  description = "OCID of the Oracle Cloud VCN"
  type        = string
}

variable "oci_vcn_cidr" {
  description = "CIDR block of the Oracle Cloud VCN"
  type        = string
}

# Tencent Cloud Variables
variable "tencent_secret_id" {
  description = "Secret ID for Tencent Cloud API"
  type        = string
}

variable "tencent_secret_key" {
  description = "Secret key for Tencent Cloud API"
  type        = string
  sensitive   = true
}

variable "tencent_region" {
  description = "Tencent Cloud region"
  type        = string
}

variable "tencent_vpc_id" {
  description = "ID of the Tencent Cloud VPC"
  type        = string
}

variable "tencent_vpc_cidr" {
  description = "CIDR block of the Tencent Cloud VPC"
  type        = string
}

# Removed tencent_route_table_id as we're creating a new route table

variable "tencent_availability_zone" {
  description = "Availability zone for Tencent Cloud resources"
  type        = string
  default     = "ap-singapore-1"
}

# Shared VPN Variables
variable "ipsec_psk" {
  description = "Pre-shared key for IPSec VPN connection"
  type        = string
  sensitive   = true
}

# Common VPN Configuration Variables
variable "ike_version" {
  description = "IKE protocol version"
  type        = string
  default     = "2"
}

variable "encryption_algorithm" {
  description = "Encryption algorithm for IPSec"
  type        = string
  default     = "AES-256"
}

variable "authentication_algorithm" {
  description = "Authentication algorithm for IPSec"
  type        = string
  default     = "SHA2-256"
}

variable "dh_group" {
  description = "Diffie-Hellman group"
  type        = string
  default     = "GROUP14"
}

variable "ike_lifetime" {
  description = "Lifetime for IKE SA in seconds"
  type        = number
  default     = 28800
}

variable "ipsec_lifetime" {
  description = "Lifetime for IPSec SA in seconds"
  type        = number
  default     = 3600
}