# SSH key for all nodes
resource "tencentcloud_key_pair" "cluster_key" {
  key_name   = "${var.cluster_name}_key"
  public_key = file(pathexpand(var.ssh_public_key_path))
}

# Kubernetes Cluster
resource "tencentcloud_kubernetes_cluster" "k8s_cluster" {
  vpc_id                          = tencentcloud_vpc.k8s_vpc.id
  cluster_name                    = var.cluster_name
  cluster_version                 = var.cluster_version
  cluster_max_pod_num             = 256
  cluster_max_service_num         = 4096
  cluster_deploy_type             = "MANAGED_CLUSTER"
  cluster_internet_security_group = tencentcloud_security_group.cluster_sg.id
  container_runtime               = "containerd"
  network_type                    = "VPC-CNI"
  eni_subnet_ids                  = [tencentcloud_subnet.eni_pod_subnet.id]
  service_cidr                    = var.service_cidr
}

resource "tencentcloud_kubernetes_cluster_endpoint" "cluster_endpoint" {
  cluster_id                      = tencentcloud_kubernetes_cluster.k8s_cluster.id
  cluster_internet                = false  # No public internet access
  cluster_intranet                = true   # Private network access
  cluster_internet_security_group = tencentcloud_security_group.cluster_sg.id
  cluster_intranet_subnet_id      = tencentcloud_subnet.private_subnet.id

  # KEY: Wait for node pool to be ready (from official docs)
  depends_on = [
    tencentcloud_kubernetes_node_pool.tools
  ]
}