# Region settings
region = "sa-saopaulo"
availability_zones = ["sa-saopaulo-1", "sa-saopaulo-2", "sa-saopaulo-3"]

# Path to your SSH public key file
ssh_public_key_path = "~/.ssh/id_rsa.pub"

# Cluster CIDR
cluster_cidr = "172.16.0.0/16"

# VPC and subnet settings
vpc_cidr = "10.0.0.0/16"

# Cluster settings
cluster_name = "wall"

# Node settings
node_instance_type = "S5.2XLARGE16"  # 8 vCPU - 32GB RAM - Intel
node_disk_size = 100

# credentials
secret_id = ""
secret_key = ""