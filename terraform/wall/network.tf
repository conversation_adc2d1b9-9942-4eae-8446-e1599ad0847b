# VPC
resource "tencentcloud_vpc" "k8s_vpc" {
  name         = "${var.cluster_name}-vpc"
  cidr_block   = var.vpc_cidr
  is_multicast = false
  dns_servers  = ["183.60.83.19", "183.60.82.98", "1.1.1.1"]

  tags = {
    terraform = "true"
    cluster   = var.cluster_name
  }
}

# Public subnet for load balancers
resource "tencentcloud_subnet" "public_subnet" {
  name              = "public-subnet"
  vpc_id            = tencentcloud_vpc.k8s_vpc.id
  cidr_block        = var.public_subnet_cidr
  availability_zone = var.availability_zones[0]
  is_multicast      = false

  tags = {
    terraform = "true"
    cluster   = var.cluster_name
    usage     = "load-balancers"
  }
}

# Private subnet for most nodes
resource "tencentcloud_subnet" "private_subnet" {
  name              = "private-subnet"
  vpc_id            = tencentcloud_vpc.k8s_vpc.id
  cidr_block        = var.private_subnet_cidr
  availability_zone = var.availability_zones[0]
  is_multicast      = false

  tags = {
    terraform = "true"
    cluster   = var.cluster_name
    usage     = "nodes"
  }
}

# Public WhatsApp subnet
resource "tencentcloud_subnet" "public_whatsapp" {
  name              = "public-whatsapp"
  vpc_id            = tencentcloud_vpc.k8s_vpc.id
  cidr_block        = var.public_whatsapp_cidr
  availability_zone = var.availability_zones[0]
  is_multicast      = false

  tags = {
    terraform = "true"
    cluster   = var.cluster_name
    usage     = "whatsapp"
  }
}

# NAT Gateway for private subnet
resource "tencentcloud_nat_gateway" "nat_gateway" {
  name      = "${var.cluster_name}-nat"
  vpc_id    = tencentcloud_vpc.k8s_vpc.id
  bandwidth = 100
  assigned_eip_set = [
    tencentcloud_eip.nat_eip.public_ip
  ]
}

# ENI subnet for pod IPs
resource "tencentcloud_subnet" "eni_pod_subnet" {
  name              = "eni-pod-subnet"
  vpc_id            = tencentcloud_vpc.k8s_vpc.id
  cidr_block        = var.eni_pod_subnet_cidr
  availability_zone = var.availability_zones[0]
  is_multicast      = false

  tags = {
    terraform = "true"
    cluster   = var.cluster_name
    usage     = "pod-ips"
  }
}

# Route table association for ENI subnet
resource "tencentcloud_route_table_association" "eni_pod_association" {
  subnet_id      = tencentcloud_subnet.eni_pod_subnet.id
  route_table_id = tencentcloud_route_table.private_route_table.id
}

# EIP for NAT Gateway
resource "tencentcloud_eip" "nat_eip" {
  name = "${var.cluster_name}-nat-eip"
}

# Route Table for private subnet
resource "tencentcloud_route_table" "private_route_table" {
  vpc_id = tencentcloud_vpc.k8s_vpc.id
  name   = "private-route-table"
}

# Route for internet access via NAT Gateway
resource "tencentcloud_route_table_entry" "internet_route" {
  route_table_id         = tencentcloud_route_table.private_route_table.id
  destination_cidr_block = "0.0.0.0/0"
  next_type             = "NAT"
  next_hub              = tencentcloud_nat_gateway.nat_gateway.id
}

# Associate route table with private subnet
resource "tencentcloud_route_table_association" "private_association" {
  subnet_id      = tencentcloud_subnet.private_subnet.id
  route_table_id = tencentcloud_route_table.private_route_table.id
}

# Security Group for cluster
resource "tencentcloud_security_group" "cluster_sg" {
  name        = "${var.cluster_name}-sg"
  description = "Security group for Kubernetes cluster"
}

# Security group rules
resource "tencentcloud_security_group_rule" "cluster_internal_ingress_vpc" {
  security_group_id = tencentcloud_security_group.cluster_sg.id
  type             = "ingress"
  cidr_ip          = "10.0.0.0/16"
  ip_protocol      = "ALL"
  policy           = "ACCEPT"
}

resource "tencentcloud_security_group_rule" "cluster_internal_ingress_ssh" {
  security_group_id = tencentcloud_security_group.cluster_sg.id
  type             = "ingress"
  cidr_ip          = "0.0.0.0/0"
  ip_protocol      = "TCP"
  port_range       = "22"
  policy           = "ACCEPT"
}

resource "tencentcloud_security_group_rule" "cluster_internal_ingress_k8s_api" {
  security_group_id = tencentcloud_security_group.cluster_sg.id
  type             = "ingress"
  cidr_ip          = "0.0.0.0/0"
  ip_protocol      = "TCP"
  port_range       = "6443"
  policy           = "ACCEPT"
}

resource "tencentcloud_security_group_rule" "cluster_internal_ingress_http" {
  security_group_id = tencentcloud_security_group.cluster_sg.id
  type             = "ingress"
  cidr_ip          = "0.0.0.0/0"
  ip_protocol      = "TCP"
  port_range       = "80,443"
  policy           = "ACCEPT"
}

resource "tencentcloud_security_group_rule" "cluster_internal_egress" {
  security_group_id = tencentcloud_security_group.cluster_sg.id
  type             = "egress"
  cidr_ip          = "0.0.0.0/0"
  ip_protocol      = "ALL"
  policy           = "ACCEPT"
}