{"version": 4, "terraform_version": "1.11.0", "serial": 252, "lineage": "85ad3577-5872-2247-1cd0-6813365474a4", "outputs": {"bastion_public_ip": {"value": "**************", "type": "string"}, "cluster_ca_certificate": {"value": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIICyDCCAbCgAwIBAgIBADANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDEwprdWJl\ncm5ldGVzMB4XDTI1MDYxMDIxMDUwMVoXDTM1MDYwODIxMDUwMVowFTETMBEGA1UE\nAxMKa3ViZXJuZXRlczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAM5+\noYl/xvF4nf0Q/i4uko5plktmoNj7I7tfYhHSYJoXjDgW5lwGKRK0S0O2jI3nMujJ\nQkfqZTLBGX9j5Dy6yfHn3zmCy/bwEAWawFRZEU1nAq/JJ1XltuuDheZK1uQUwmlE\nLXEIhTEut3mg0mCKZuxRePkfoboLz+XD0uZ0kSP+RQQwh8q+1qwYKSyFdxEGd5XT\nul5MlNTmBpHHV02Tc5fK01oLmSYCglwezg8eXggywDV7ajVAb+qg1T696WeSEnaS\nX7fwpymx4rir+O922TIW1Bh2RS6qLuG8nfHAvnq3XtpibVCbPhQiNfdzAg8WGiJ8\nnojy6ig2040e4wP/BqMCAwEAAaMjMCEwDgYDVR0PAQH/BAQDAgKUMA8GA1UdEwEB\n/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAJ1Yd89GtfojkqJI12CEaVEhzQlS\nVbv6j4xs+gSDTySEXHjkaELyoFYJAQIJBmQGXijf0zSxfYWyXvg1BnNkU6P5d89d\nNeDVEMr1VAVenlbFAD1hiHXfbk16pwiufBH5hAmJG1aW1FwiqNrxkCPX+j5tA8bp\nZOjxB7qVLi91LKt0dDG11+zI92fhLK+6FLn+VkXH04eE1VXuCyu0OaAovgjIiNNt\nKQbNM9UkDkLQfoxQlnjezy52qBkvvdOiD7Mgf1D/yWTwefFNW3RD2BuCypLFEMhe\nY44GyUmjcN+r5oMaDKLcoyR3ycd3uBGLqgrCxU5Lx+ebxUrDv4RVKzRGTWc=\n-----END CERTIFICATE-----\n", "type": "string", "sensitive": true}, "cluster_id": {"value": "cls-cv769q2x", "type": "string"}, "cluster_internal_endpoint": {"value": "*********", "type": "string"}, "cluster_kubeconfig": {"value": "", "type": "string", "sensitive": true}, "kubectl_config_raw": {"value": "apiVersion: v1\nclusters:\n- cluster:\n    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN5RENDQWJDZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJMU1EWXhNREl4TURVd01Wb1hEVE0xTURZd09ESXhNRFV3TVZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTTUrCm9ZbC94dkY0bmYwUS9pNHVrbzVwbGt0bW9OajdJN3RmWWhIU1lKb1hqRGdXNWx3R0tSSzBTME8yakkzbk11akoKUWtmcVpUTEJHWDlqNUR5NnlmSG4zem1DeS9id0VBV2F3RlJaRVUxbkFxL0pKMVhsdHV1RGhlWksxdVFVd21sRQpMWEVJaFRFdXQzbWcwbUNLWnV4UmVQa2ZvYm9MeitYRDB1WjBrU1ArUlFRd2g4cSsxcXdZS1N5RmR4RUdkNVhUCnVsNU1sTlRtQnBISFYwMlRjNWZLMDFvTG1TWUNnbHdlemc4ZVhnZ3l3RFY3YWpWQWIrcWcxVDY5NldlU0VuYVMKWDdmd3B5bXg0cmlyK085MjJUSVcxQmgyUlM2cUx1RzhuZkhBdm5xM1h0cGliVkNiUGhRaU5mZHpBZzhXR2lKOApub2p5NmlnMjA0MGU0d1AvQnFNQ0F3RUFBYU1qTUNFd0RnWURWUjBQQVFIL0JBUURBZ0tVTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFKMVlkODlHdGZvamtxSkkxMkNFYVZFaHpRbFMKVmJ2Nmo0eHMrZ1NEVHlTRVhIamthRUx5b0ZZSkFRSUpCbVFHWGlqZjB6U3hmWVd5WHZnMUJuTmtVNlA1ZDg5ZApOZURWRU1yMVZBVmVubGJGQUQxaGlIWGZiazE2cHdpdWZCSDVoQW1KRzFhVzFGd2lxTnJ4a0NQWCtqNXRBOGJwClpPanhCN3FWTGk5MUxLdDBkREcxMSt6STkyZmhMSys2RkxuK1ZrWEgwNGVFMVZYdUN5dTBPYUFvdmdqSWlOTnQKS1FiTk05VWtEa0xRZm94UWxuamV6eTUycUJrdnZkT2lEN01nZjFEL3lXVHdlZkZOVzNSRDJCdUN5cExGRU1oZQpZNDRHeVVtamNOK3I1b01hREtMY295UjN5Y2QzdUJHTHFnckN4VTVMeCtlYnhVckR2NFJWS3pSR1RXYz0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=\n    server: https://*********\n  name: cls-cv769q2x\ncontexts:\n- context:\n    cluster: cls-cv769q2x\n    user: \"200038622307\"\n  name: cls-cv769q2x-200038622307-context-default\ncurrent-context: cls-cv769q2x-200038622307-context-default\nkind: Config\npreferences: {}\nusers:\n- name: \"200038622307\"\n  user:\n    client-certificate-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURERENDQWZTZ0F3SUJBZ0lJR011bURrMnV5dXd3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1UQXlNVEE0TXpKYUZ3MDBOVEEyTVRBeU1UQTRNekZhTURZeApFakFRQmdOVkJBb1RDWFJyWlRwMWMyVnljekVnTUI0R0ExVUVBeE1YTWpBd01ETTROakl5TXpBM0xURTNORGsxCk9EazNNVEV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRFBzL3dSSmFjcEd3QXUKT3JmVlhERmhVRUJNdGVxK0RFUmlGSEVuVFlSbkJ5ZzFtRy9mR3BySWgyZTA4SVJUZXVRK3dhbVVSQld2MmRwLwpPQTJJekhsRVFLamRpQmVYNC91RG5EN0s3OUdPWGY0d0tXd3NYbFF3RVFpQ1J2cW80cllXLzV4TW1qZ0FrdGw0CjhpczBsVmxpaGgyK3ZQaEt6V2dGZmYvaEFJdXAyK1A1dm9GZzducmxXRTZXYTRJSHIxd0hyMGhEUEtPdFFVd28KYTNudUZ4b1I5a2VIMVlLdkRCMk5aay90L1pPRzdZL05pelBoMkFFcHRid3ZGeU9KQnRTbUcwMzF3ZWtFeE5TbgpMSWhuUGRnZDFTSTBCeDZZNWQxKzN4SGMxNDJKb25ZbmJFQ3BCTWg4RUVFcUh0WExJVW9MN0xkREpXWnJ1QTRpCmtrMDVQK2pEQWdNQkFBR2pQekE5TUE0R0ExVWREd0VCL3dRRUF3SUNoREFkQmdOVkhTVUVGakFVQmdnckJnRUYKQlFjREFnWUlLd1lCQlFVSEF3RXdEQVlEVlIwVEFRSC9CQUl3QURBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQQpjSnhESyt3OGswR3hXVzc0bWNEVlp6TVVvOTlJd2FXUmhUOG54L1k4a0lsbXNtMTFzL1JhWkd3VlhsSGVwMFdWCnhrbmNySS8xb25jQU1JNkwwV3ZkQWtQSDFVTUljbXY3RUdnd1oxdENzVWg3bmZyZ3ZKR1RhT2xhK0Z4MmEyUWsKS01mVWVsdWZpMWFqLzlmN2R3cXJDVWtIWHhxZE8rNjZRa3RXcEZ0am1oVDJaZDdWcUp0SDJPaHJNZGtUU1grcQoxU3hvbjJaWEpCeGpCazhYZDlXOEs4UzdHdnJuQlVaUzJCWHZlZjB0NTZubWpqN3JiQVcxcUFPYjdWeXN1WHBRCldzUWFabGlxdDB6OXRyR2FCZnV1NzNiZThnMWlVOG1sOUFOZ0h6SEpvR25Xa2ZPTCtlWTFaVVZaaWdBOHVqVW4KZEhxNlNOYS9EeE1RVHI2cWR1bGFRdz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K\n    client-key-data: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n", "type": "string", "sensitive": true}, "nat_eip_address": {"value": "************", "type": "string"}, "nat_gateway_id": {"value": "nat-841muwfq", "type": "string"}, "private_subnet_id": {"value": "subnet-hdgyvnq6", "type": "string"}, "public_subnet_id": {"value": "subnet-gat724b6", "type": "string"}, "public_whatsapp_subnet_id": {"value": "subnet-51e1otso", "type": "string"}, "stateful_node_pool_id": {"value": "cls-cv769q2x#np-2047d6q5", "type": "string"}, "stateless_fallback_node_pool_id": {"value": "cls-cv769q2x#np-3iu4xiyz", "type": "string"}, "stateless_spot_node_pool_id": {"value": "cls-cv769q2x#np-8g3nier1", "type": "string"}, "tools_node_pool_id": {"value": "cls-cv769q2x#np-kbvmi15x", "type": "string"}, "vpc_id": {"value": "vpc-qm4rn4zz", "type": "string"}, "whatsapp_fallback_node_pool_id": {"value": "cls-cv769q2x#np-c7b3g5er", "type": "string"}, "whatsapp_spot_node_pool_id": {"value": "cls-cv769q2x#np-bb2394o9", "type": "string"}}, "resources": [{"mode": "managed", "type": "tencentcloud_eip", "name": "nat_eip", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"anti_ddos_package_id": null, "anycast_zone": null, "applicable_for_clb": null, "auto_renew_flag": null, "bandwidth_package_id": null, "cdc_id": null, "egress": "center_egress1", "id": "eip-dt3tlozh", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 1, "internet_service_provider": null, "name": "wall-nat-eip", "prepaid_period": null, "public_ip": "************", "status": "BIND", "tags": {}, "type": "EIP"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "tencentcloud_instance", "name": "bastion", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"allocate_public_ip": true, "availability_zone": "sa-saopaulo-1", "bandwidth_package_id": null, "cam_role_name": "", "cdh_host_id": null, "cdh_instance_type": null, "cpu": 2, "create_time": "2025-06-10T21:03:05Z", "data_disks": [], "dedicated_cluster_id": "", "disable_api_termination": false, "disable_automation_service": false, "disable_monitor_service": false, "disable_security_service": false, "expired_time": "", "force_delete": false, "force_replace_placement_group_id": null, "hostname": null, "hpc_cluster_id": "", "id": "ins-81eu5r89", "image_id": "img-22trbn9x", "instance_charge_type": "POSTPAID_BY_HOUR", "instance_charge_type_prepaid_period": null, "instance_charge_type_prepaid_renew_flag": "", "instance_count": null, "instance_name": "wall-bastion", "instance_status": "RUNNING", "instance_type": "S5.MEDIUM2", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 10, "keep_image_login": null, "key_ids": ["skey-furakmzf"], "key_name": "skey-furakmzf", "memory": 2, "orderly_security_groups": ["sg-2jx9w8fx"], "os_name": "Ubuntu Server 20.04 LTS 64bit", "password": null, "placement_group_id": "", "private_ip": "********", "project_id": 0, "public_ip": "**************", "running_flag": true, "security_groups": ["sg-2jx9w8fx"], "spot_instance_type": null, "spot_max_price": null, "stopped_mode": null, "subnet_id": "subnet-gat724b6", "system_disk_id": "disk-hpdo558f", "system_disk_name": "wall-bastion_SYSTEM_DISK", "system_disk_resize_online": null, "system_disk_size": 50, "system_disk_type": "CLOUD_PREMIUM", "tags": {"cluster": "wall", "component": "bastion", "terraform": "true"}, "timeouts": null, "user_data": "", "user_data_raw": "", "uuid": "6762668f-078d-4767-8dc6-4b9a3fcd0868", "vpc_id": "vpc-qm4rn4zz"}, "sensitive_attributes": [[{"type": "get_attr", "value": "password"}]], "private": "****************************************************************************************", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_security_group.bastion_sg", "tencentcloud_subnet.public_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_key_pair", "name": "cluster_key", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"created_time": "2025-03-11T18:37:28Z", "id": "skey-furakmzf", "key_name": "wall_key", "private_key": null, "project_id": 0, "public_key": "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC0cj8YdQ8sII7rCjONRK8nO8CP3yOK46bLYedqJlCUb8lyiIFnNd0D/xgGllMbFe2nEn9HnuyYrYPIJelNLA2b7Jw4jr7RLizKuVIfie6HDY6nIfaoUvxqNqE+3f1wuZXFi36jh7+9Yxm7fViSJZTssy0vld6VE57ynrCiRfjk0MCcLDN35cD6P4P3Tpg8RxdKrXDXyXgOtTPLfEGXgW8C7H1ojDx+LfKLItnevm38LBggfaHaMqs3+ByFr+HTlTBz5y9LSFn2yHOLissd+7CkttGaYJ7bAck97XBBMDGtwf8LMlHBw0oBhJAhlItGO9yc4n4kJFX73pyFNK/MqEM04fZN+9z2FIVWzlPlMxNP1q+DQTC3rJQ27VTTxi582stw4m3+j3s1bRcQiUMpA6au4IALtdn1AfBcnirgrzBCm4+3BWMGQWJhNr5yRyJev0U2wLgI5Q3twidPaytZIk0OsWYITsx3zD+uNr9uNhabp9MkonNsvm1rQX/9cnpr1uzEjEo3ZYLT/QZFEzkrobA2K40OZ2VKMT26sBQyaDIbwg6dUW269VnjIGL2blgbcDoIECicB1ZUhVTlo6EZMGVx8tlxup6qc5gRXlrXnHbBWXaCsvNbJA5S1MinQIYDdBARjf4aIqZBwCFshCmAu5goqtuauzdytFQTKap3hGJ/vw==", "tags": {}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_cluster", "name": "k8s_cluster", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"acquire_cluster_admin_role": null, "auth_options": [], "auto_upgrade_cluster_level": null, "base_pod_num": null, "cdc_id": "", "certification_authority": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIICyDCCAbCgAwIBAgIBADANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDEwprdWJl\ncm5ldGVzMB4XDTI1MDYxMDIxMDUwMVoXDTM1MDYwODIxMDUwMVowFTETMBEGA1UE\nAxMKa3ViZXJuZXRlczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAM5+\noYl/xvF4nf0Q/i4uko5plktmoNj7I7tfYhHSYJoXjDgW5lwGKRK0S0O2jI3nMujJ\nQkfqZTLBGX9j5Dy6yfHn3zmCy/bwEAWawFRZEU1nAq/JJ1XltuuDheZK1uQUwmlE\nLXEIhTEut3mg0mCKZuxRePkfoboLz+XD0uZ0kSP+RQQwh8q+1qwYKSyFdxEGd5XT\nul5MlNTmBpHHV02Tc5fK01oLmSYCglwezg8eXggywDV7ajVAb+qg1T696WeSEnaS\nX7fwpymx4rir+O922TIW1Bh2RS6qLuG8nfHAvnq3XtpibVCbPhQiNfdzAg8WGiJ8\nnojy6ig2040e4wP/BqMCAwEAAaMjMCEwDgYDVR0PAQH/BAQDAgKUMA8GA1UdEwEB\n/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAJ1Yd89GtfojkqJI12CEaVEhzQlS\nVbv6j4xs+gSDTySEXHjkaELyoFYJAQIJBmQGXijf0zSxfYWyXvg1BnNkU6P5d89d\nNeDVEMr1VAVenlbFAD1hiHXfbk16pwiufBH5hAmJG1aW1FwiqNrxkCPX+j5tA8bp\nZOjxB7qVLi91LKt0dDG11+zI92fhLK+6FLn+VkXH04eE1VXuCyu0OaAovgjIiNNt\nKQbNM9UkDkLQfoxQlnjezy52qBkvvdOiD7Mgf1D/yWTwefFNW3RD2BuCypLFEMhe\nY44GyUmjcN+r5oMaDKLcoyR3ycd3uBGLqgrCxU5Lx+ebxUrDv4RVKzRGTWc=\n-----END CERTIFICATE-----\n", "claim_expired_seconds": 300, "cluster_as_enabled": null, "cluster_audit": [], "cluster_cidr": "", "cluster_deploy_type": "MANAGED_CLUSTER", "cluster_desc": "", "cluster_external_endpoint": "", "cluster_extra_args": [], "cluster_internet": false, "cluster_internet_domain": null, "cluster_internet_security_group": "sg-9gseiv0b", "cluster_intranet": true, "cluster_intranet_domain": "", "cluster_intranet_subnet_id": "subnet-hdgyvnq6", "cluster_ipvs": true, "cluster_level": "L5", "cluster_max_pod_num": 256, "cluster_max_service_num": 4096, "cluster_name": "wall", "cluster_node_num": 4, "cluster_os": "tlinux2.4x86_64", "cluster_os_type": "GENERAL", "cluster_subnet_id": null, "cluster_version": "1.30.0", "container_runtime": "containerd", "deletion_protection": false, "docker_graph_path": null, "domain": "cls-cv769q2x.ccs.tencent-cloud.com", "enable_customized_pod_cidr": false, "eni_subnet_ids": ["subnet-4fx3fmm0"], "event_persistence": [], "exist_instance": [], "extension_addon": [], "extra_args": null, "globe_desired_pod_num": null, "id": "cls-cv769q2x", "ignore_cluster_cidr_conflict": false, "ignore_service_cidr_conflict": false, "instance_delete_mode": null, "is_dual_stack": null, "is_non_static_ip_mode": false, "kube_config": "", "kube_config_intranet": "apiVersion: v1\nclusters:\n- cluster:\n    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN5RENDQWJDZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJMU1EWXhNREl4TURVd01Wb1hEVE0xTURZd09ESXhNRFV3TVZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTTUrCm9ZbC94dkY0bmYwUS9pNHVrbzVwbGt0bW9OajdJN3RmWWhIU1lKb1hqRGdXNWx3R0tSSzBTME8yakkzbk11akoKUWtmcVpUTEJHWDlqNUR5NnlmSG4zem1DeS9id0VBV2F3RlJaRVUxbkFxL0pKMVhsdHV1RGhlWksxdVFVd21sRQpMWEVJaFRFdXQzbWcwbUNLWnV4UmVQa2ZvYm9MeitYRDB1WjBrU1ArUlFRd2g4cSsxcXdZS1N5RmR4RUdkNVhUCnVsNU1sTlRtQnBISFYwMlRjNWZLMDFvTG1TWUNnbHdlemc4ZVhnZ3l3RFY3YWpWQWIrcWcxVDY5NldlU0VuYVMKWDdmd3B5bXg0cmlyK085MjJUSVcxQmgyUlM2cUx1RzhuZkhBdm5xM1h0cGliVkNiUGhRaU5mZHpBZzhXR2lKOApub2p5NmlnMjA0MGU0d1AvQnFNQ0F3RUFBYU1qTUNFd0RnWURWUjBQQVFIL0JBUURBZ0tVTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFKMVlkODlHdGZvamtxSkkxMkNFYVZFaHpRbFMKVmJ2Nmo0eHMrZ1NEVHlTRVhIamthRUx5b0ZZSkFRSUpCbVFHWGlqZjB6U3hmWVd5WHZnMUJuTmtVNlA1ZDg5ZApOZURWRU1yMVZBVmVubGJGQUQxaGlIWGZiazE2cHdpdWZCSDVoQW1KRzFhVzFGd2lxTnJ4a0NQWCtqNXRBOGJwClpPanhCN3FWTGk5MUxLdDBkREcxMSt6STkyZmhMSys2RkxuK1ZrWEgwNGVFMVZYdUN5dTBPYUFvdmdqSWlOTnQKS1FiTk05VWtEa0xRZm94UWxuamV6eTUycUJrdnZkT2lEN01nZjFEL3lXVHdlZkZOVzNSRDJCdUN5cExGRU1oZQpZNDRHeVVtamNOK3I1b01hREtMY295UjN5Y2QzdUJHTHFnckN4VTVMeCtlYnhVckR2NFJWS3pSR1RXYz0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=\n    server: https://*********\n  name: cls-cv769q2x\ncontexts:\n- context:\n    cluster: cls-cv769q2x\n    user: \"200038622307\"\n  name: cls-cv769q2x-200038622307-context-default\ncurrent-context: cls-cv769q2x-200038622307-context-default\nkind: Config\npreferences: {}\nusers:\n- name: \"200038622307\"\n  user:\n    client-certificate-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURERENDQWZTZ0F3SUJBZ0lJR011bURrMnV5dXd3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1UQXlNVEE0TXpKYUZ3MDBOVEEyTVRBeU1UQTRNekZhTURZeApFakFRQmdOVkJBb1RDWFJyWlRwMWMyVnljekVnTUI0R0ExVUVBeE1YTWpBd01ETTROakl5TXpBM0xURTNORGsxCk9EazNNVEV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRFBzL3dSSmFjcEd3QXUKT3JmVlhERmhVRUJNdGVxK0RFUmlGSEVuVFlSbkJ5ZzFtRy9mR3BySWgyZTA4SVJUZXVRK3dhbVVSQld2MmRwLwpPQTJJekhsRVFLamRpQmVYNC91RG5EN0s3OUdPWGY0d0tXd3NYbFF3RVFpQ1J2cW80cllXLzV4TW1qZ0FrdGw0CjhpczBsVmxpaGgyK3ZQaEt6V2dGZmYvaEFJdXAyK1A1dm9GZzducmxXRTZXYTRJSHIxd0hyMGhEUEtPdFFVd28KYTNudUZ4b1I5a2VIMVlLdkRCMk5aay90L1pPRzdZL05pelBoMkFFcHRid3ZGeU9KQnRTbUcwMzF3ZWtFeE5TbgpMSWhuUGRnZDFTSTBCeDZZNWQxKzN4SGMxNDJKb25ZbmJFQ3BCTWg4RUVFcUh0WExJVW9MN0xkREpXWnJ1QTRpCmtrMDVQK2pEQWdNQkFBR2pQekE5TUE0R0ExVWREd0VCL3dRRUF3SUNoREFkQmdOVkhTVUVGakFVQmdnckJnRUYKQlFjREFnWUlLd1lCQlFVSEF3RXdEQVlEVlIwVEFRSC9CQUl3QURBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQQpjSnhESyt3OGswR3hXVzc0bWNEVlp6TVVvOTlJd2FXUmhUOG54L1k4a0lsbXNtMTFzL1JhWkd3VlhsSGVwMFdWCnhrbmNySS8xb25jQU1JNkwwV3ZkQWtQSDFVTUljbXY3RUdnd1oxdENzVWg3bmZyZ3ZKR1RhT2xhK0Z4MmEyUWsKS01mVWVsdWZpMWFqLzlmN2R3cXJDVWtIWHhxZE8rNjZRa3RXcEZ0am1oVDJaZDdWcUp0SDJPaHJNZGtUU1grcQoxU3hvbjJaWEpCeGpCazhYZDlXOEs4UzdHdnJuQlVaUzJCWHZlZjB0NTZubWpqN3JiQVcxcUFPYjdWeXN1WHBRCldzUWFabGlxdDB6OXRyR2FCZnV1NzNiZThnMWlVOG1sOUFOZ0h6SEpvR25Xa2ZPTCtlWTFaVVZaaWdBOHVqVW4KZEhxNlNOYS9EeE1RVHI2cWR1bGFRdz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K\n    client-key-data: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n", "kube_proxy_mode": null, "labels": null, "log_agent": [], "managed_cluster_internet_security_policies": null, "master_config": [], "mount_target": null, "network_type": "VPC-CNI", "node_name_type": "lan-ip", "node_pool_global_config": [{"expander": "random", "ignore_daemon_sets_utilization": false, "is_scale_in_enabled": false, "max_concurrent_scale_in": 10, "scale_in_delay": 10, "scale_in_unneeded_time": 10, "scale_in_utilization_threshold": 50, "skip_nodes_with_local_storage": true, "skip_nodes_with_system_pods": true}], "password": "K1gLVRAhzHYnuakwixwIxhHAkgWNvKSo", "pgw_endpoint": "*********", "pre_start_user_script": null, "project_id": 0, "resource_delete_options": [], "runtime_version": null, "security_policy": [], "service_cidr": "**********/20", "tags": {}, "unschedulable": null, "upgrade_instances_follow_cluster": false, "user_name": "admin", "vpc_cni_type": "tke-route-eni", "vpc_id": "vpc-qm4rn4zz", "worker_config": [], "worker_instances_list": [{"failed_reason": "=Ready:True", "instance_id": "ins-fkgoe7jv", "instance_role": "WORKER", "instance_state": "running", "lan_ip": "********"}, {"failed_reason": "=Ready:True", "instance_id": "ins-jauklgc5", "instance_role": "WORKER", "instance_state": "running", "lan_ip": "********"}, {"failed_reason": "=Ready:True", "instance_id": "ins-mpetnhyr", "instance_role": "WORKER", "instance_state": "running", "lan_ip": "*********"}, {"failed_reason": "=Ready:True", "instance_id": "ins-43ljur5r", "instance_role": "WORKER", "instance_state": "running", "lan_ip": "********"}]}, "sensitive_attributes": [[{"type": "get_attr", "value": "kube_config"}], [{"type": "get_attr", "value": "kube_config_intranet"}]], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.eni_pod_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_cluster_endpoint", "name": "cluster_endpoint", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"certification_authority": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIICyDCCAbCgAwIBAgIBADANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDEwprdWJl\ncm5ldGVzMB4XDTI1MDYxMDIxMDUwMVoXDTM1MDYwODIxMDUwMVowFTETMBEGA1UE\nAxMKa3ViZXJuZXRlczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAM5+\noYl/xvF4nf0Q/i4uko5plktmoNj7I7tfYhHSYJoXjDgW5lwGKRK0S0O2jI3nMujJ\nQkfqZTLBGX9j5Dy6yfHn3zmCy/bwEAWawFRZEU1nAq/JJ1XltuuDheZK1uQUwmlE\nLXEIhTEut3mg0mCKZuxRePkfoboLz+XD0uZ0kSP+RQQwh8q+1qwYKSyFdxEGd5XT\nul5MlNTmBpHHV02Tc5fK01oLmSYCglwezg8eXggywDV7ajVAb+qg1T696WeSEnaS\nX7fwpymx4rir+O922TIW1Bh2RS6qLuG8nfHAvnq3XtpibVCbPhQiNfdzAg8WGiJ8\nnojy6ig2040e4wP/BqMCAwEAAaMjMCEwDgYDVR0PAQH/BAQDAgKUMA8GA1UdEwEB\n/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAJ1Yd89GtfojkqJI12CEaVEhzQlS\nVbv6j4xs+gSDTySEXHjkaELyoFYJAQIJBmQGXijf0zSxfYWyXvg1BnNkU6P5d89d\nNeDVEMr1VAVenlbFAD1hiHXfbk16pwiufBH5hAmJG1aW1FwiqNrxkCPX+j5tA8bp\nZOjxB7qVLi91LKt0dDG11+zI92fhLK+6FLn+VkXH04eE1VXuCyu0OaAovgjIiNNt\nKQbNM9UkDkLQfoxQlnjezy52qBkvvdOiD7Mgf1D/yWTwefFNW3RD2BuCypLFEMhe\nY44GyUmjcN+r5oMaDKLcoyR3ycd3uBGLqgrCxU5Lx+ebxUrDv4RVKzRGTWc=\n-----END CERTIFICATE-----\n", "cluster_deploy_type": "MANAGED_CLUSTER", "cluster_external_endpoint": "", "cluster_id": "cls-cv769q2x", "cluster_internet": false, "cluster_internet_domain": null, "cluster_internet_security_group": "sg-9gseiv0b", "cluster_intranet": true, "cluster_intranet_domain": null, "cluster_intranet_subnet_id": "subnet-hdgyvnq6", "domain": "cls-cv769q2x.ccs.tencent-cloud.com", "extensive_parameters": null, "id": "cls-cv769q2x", "kube_config": null, "kube_config_intranet": "apiVersion: v1\nclusters:\n- cluster:\n    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN5RENDQWJDZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJMU1EWXhNREl4TURVd01Wb1hEVE0xTURZd09ESXhNRFV3TVZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTTUrCm9ZbC94dkY0bmYwUS9pNHVrbzVwbGt0bW9OajdJN3RmWWhIU1lKb1hqRGdXNWx3R0tSSzBTME8yakkzbk11akoKUWtmcVpUTEJHWDlqNUR5NnlmSG4zem1DeS9id0VBV2F3RlJaRVUxbkFxL0pKMVhsdHV1RGhlWksxdVFVd21sRQpMWEVJaFRFdXQzbWcwbUNLWnV4UmVQa2ZvYm9MeitYRDB1WjBrU1ArUlFRd2g4cSsxcXdZS1N5RmR4RUdkNVhUCnVsNU1sTlRtQnBISFYwMlRjNWZLMDFvTG1TWUNnbHdlemc4ZVhnZ3l3RFY3YWpWQWIrcWcxVDY5NldlU0VuYVMKWDdmd3B5bXg0cmlyK085MjJUSVcxQmgyUlM2cUx1RzhuZkhBdm5xM1h0cGliVkNiUGhRaU5mZHpBZzhXR2lKOApub2p5NmlnMjA0MGU0d1AvQnFNQ0F3RUFBYU1qTUNFd0RnWURWUjBQQVFIL0JBUURBZ0tVTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFKMVlkODlHdGZvamtxSkkxMkNFYVZFaHpRbFMKVmJ2Nmo0eHMrZ1NEVHlTRVhIamthRUx5b0ZZSkFRSUpCbVFHWGlqZjB6U3hmWVd5WHZnMUJuTmtVNlA1ZDg5ZApOZURWRU1yMVZBVmVubGJGQUQxaGlIWGZiazE2cHdpdWZCSDVoQW1KRzFhVzFGd2lxTnJ4a0NQWCtqNXRBOGJwClpPanhCN3FWTGk5MUxLdDBkREcxMSt6STkyZmhMSys2RkxuK1ZrWEgwNGVFMVZYdUN5dTBPYUFvdmdqSWlOTnQKS1FiTk05VWtEa0xRZm94UWxuamV6eTUycUJrdnZkT2lEN01nZjFEL3lXVHdlZkZOVzNSRDJCdUN5cExGRU1oZQpZNDRHeVVtamNOK3I1b01hREtMY295UjN5Y2QzdUJHTHFnckN4VTVMeCtlYnhVckR2NFJWS3pSR1RXYz0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=\n    server: https://*********\n  name: cls-cv769q2x\ncontexts:\n- context:\n    cluster: cls-cv769q2x\n    user: \"200038622307\"\n  name: cls-cv769q2x-200038622307-context-default\ncurrent-context: cls-cv769q2x-200038622307-context-default\nkind: Config\npreferences: {}\nusers:\n- name: \"200038622307\"\n  user:\n    client-certificate-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURERENDQWZTZ0F3SUJBZ0lJR011bURrMnV5dXd3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1UQXlNVEE0TXpKYUZ3MDBOVEEyTVRBeU1UQTRNekZhTURZeApFakFRQmdOVkJBb1RDWFJyWlRwMWMyVnljekVnTUI0R0ExVUVBeE1YTWpBd01ETTROakl5TXpBM0xURTNORGsxCk9EazNNVEV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRRFBzL3dSSmFjcEd3QXUKT3JmVlhERmhVRUJNdGVxK0RFUmlGSEVuVFlSbkJ5ZzFtRy9mR3BySWgyZTA4SVJUZXVRK3dhbVVSQld2MmRwLwpPQTJJekhsRVFLamRpQmVYNC91RG5EN0s3OUdPWGY0d0tXd3NYbFF3RVFpQ1J2cW80cllXLzV4TW1qZ0FrdGw0CjhpczBsVmxpaGgyK3ZQaEt6V2dGZmYvaEFJdXAyK1A1dm9GZzducmxXRTZXYTRJSHIxd0hyMGhEUEtPdFFVd28KYTNudUZ4b1I5a2VIMVlLdkRCMk5aay90L1pPRzdZL05pelBoMkFFcHRid3ZGeU9KQnRTbUcwMzF3ZWtFeE5TbgpMSWhuUGRnZDFTSTBCeDZZNWQxKzN4SGMxNDJKb25ZbmJFQ3BCTWg4RUVFcUh0WExJVW9MN0xkREpXWnJ1QTRpCmtrMDVQK2pEQWdNQkFBR2pQekE5TUE0R0ExVWREd0VCL3dRRUF3SUNoREFkQmdOVkhTVUVGakFVQmdnckJnRUYKQlFjREFnWUlLd1lCQlFVSEF3RXdEQVlEVlIwVEFRSC9CQUl3QURBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQQpjSnhESyt3OGswR3hXVzc0bWNEVlp6TVVvOTlJd2FXUmhUOG54L1k4a0lsbXNtMTFzL1JhWkd3VlhsSGVwMFdWCnhrbmNySS8xb25jQU1JNkwwV3ZkQWtQSDFVTUljbXY3RUdnd1oxdENzVWg3bmZyZ3ZKR1RhT2xhK0Z4MmEyUWsKS01mVWVsdWZpMWFqLzlmN2R3cXJDVWtIWHhxZE8rNjZRa3RXcEZ0am1oVDJaZDdWcUp0SDJPaHJNZGtUU1grcQoxU3hvbjJaWEpCeGpCazhYZDlXOEs4UzdHdnJuQlVaUzJCWHZlZjB0NTZubWpqN3JiQVcxcUFPYjdWeXN1WHBRCldzUWFabGlxdDB6OXRyR2FCZnV1NzNiZThnMWlVOG1sOUFOZ0h6SEpvR25Xa2ZPTCtlWTFaVVZaaWdBOHVqVW4KZEhxNlNOYS9EeE1RVHI2cWR1bGFRdz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K\n    client-key-data: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n", "managed_cluster_internet_security_policies": null, "password": "K1gLVRAhzHYnuakwixwIxhHAkgWNvKSo", "pgw_endpoint": "*********", "user_name": "admin"}, "sensitive_attributes": [[{"type": "get_attr", "value": "kube_config"}], [{"type": "get_attr", "value": "password"}], [{"type": "get_attr", "value": "kube_config_intranet"}]], "private": "bnVsbA==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_kubernetes_node_pool.tools", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.eni_pod_subnet", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "stateful", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": [], "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 200, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "POSTPAID_BY_HOUR", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE32", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 0, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": false, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "", "spot_max_price": "", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-jcp1sfrd", "auto_update_instance_tags": false, "autoscaling_added_total": 1, "cluster_id": "cls-cv769q2x", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 1, "enable_auto_scale": true, "id": "cls-cv769q2x#np-2047d6q5", "labels": {"node-pool-type": "stateful", "pool": "stateful"}, "launch_config_id": "asc-lrgqzge5", "manually_added_total": 0, "max_size": 5, "min_size": 1, "multi_zone_subnet_policy": "EQUALITY", "name": "stateful", "node_config": [], "node_count": 1, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-2047d6q5", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-hdgyvnq6"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-qm4rn4zz", "wait_node_ready": null, "zones": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.eni_pod_subnet", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "stateless_fallback", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": [], "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "POSTPAID_BY_HOUR", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE32", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 0, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": false, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "", "spot_max_price": "", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-n4k82l3h", "auto_update_instance_tags": false, "autoscaling_added_total": 0, "cluster_id": "cls-cv769q2x", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 0, "enable_auto_scale": true, "id": "cls-cv769q2x#np-3iu4xiyz", "labels": {"node-pool-type": "stateless-fallback", "pool": "stateless"}, "launch_config_id": "asc-063ktcd7", "manually_added_total": 0, "max_size": 5, "min_size": 0, "multi_zone_subnet_policy": "EQUALITY", "name": "stateless-fallback", "node_config": [], "node_count": 0, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-3iu4xiyz", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-hdgyvnq6"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-qm4rn4zz", "wait_node_ready": null, "zones": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.eni_pod_subnet", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "stateless_spot", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": [], "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "SPOTPAID", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE32", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 0, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": false, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "one-time", "spot_max_price": "1000", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-pb01jgkt", "auto_update_instance_tags": false, "autoscaling_added_total": 1, "cluster_id": "cls-cv769q2x", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 1, "enable_auto_scale": true, "id": "cls-cv769q2x#np-8g3nier1", "labels": {"node-pool-type": "stateless-spot", "pool": "stateless"}, "launch_config_id": "asc-kwwbep1t", "manually_added_total": 0, "max_size": 5, "min_size": 1, "multi_zone_subnet_policy": "EQUALITY", "name": "stateless-spot", "node_config": [], "node_count": 1, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-8g3nier1", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-hdgyvnq6"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-qm4rn4zz", "wait_node_ready": null, "zones": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.eni_pod_subnet", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "tools", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": [], "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "POSTPAID_BY_HOUR", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE32", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 0, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": false, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "", "spot_max_price": "", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-7ercp5qf", "auto_update_instance_tags": false, "autoscaling_added_total": 1, "cluster_id": "cls-cv769q2x", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 1, "enable_auto_scale": true, "id": "cls-cv769q2x#np-kbvmi15x", "labels": {"node-pool-type": "tools", "pool": "tools"}, "launch_config_id": "asc-86h3xeqh", "manually_added_total": 0, "max_size": 5, "min_size": 1, "multi_zone_subnet_policy": "EQUALITY", "name": "tools", "node_config": [], "node_count": 1, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-kbvmi15x", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-hdgyvnq6"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-qm4rn4zz", "wait_node_ready": null, "zones": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.eni_pod_subnet", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "whatsapp_fallback", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": [], "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "POSTPAID_BY_HOUR", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE32", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 100, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": true, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "", "spot_max_price": "", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-0eri5vqb", "auto_update_instance_tags": false, "autoscaling_added_total": 0, "cluster_id": "cls-cv769q2x", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 0, "enable_auto_scale": true, "id": "cls-cv769q2x#np-c7b3g5er", "labels": {"node-pool-type": "whatsapp-fallback", "pool": "whatsapp"}, "launch_config_id": "asc-29kjlkex", "manually_added_total": 0, "max_size": 5, "min_size": 0, "multi_zone_subnet_policy": "EQUALITY", "name": "whatsapp-fallback", "node_config": [], "node_count": 0, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-c7b3g5er", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-51e1otso"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-qm4rn4zz", "wait_node_ready": null, "zones": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.eni_pod_subnet", "tencentcloud_subnet.public_whatsapp", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "whatsapp_spot", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": [], "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "SPOTPAID", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE32", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 100, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": true, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "one-time", "spot_max_price": "1000", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-qdjepnxf", "auto_update_instance_tags": false, "autoscaling_added_total": 1, "cluster_id": "cls-cv769q2x", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 1, "enable_auto_scale": true, "id": "cls-cv769q2x#np-bb2394o9", "labels": {"node-pool-type": "whatsapp-spot", "pool": "whatsapp"}, "launch_config_id": "asc-nk6859z3", "manually_added_total": 0, "max_size": 5, "min_size": 1, "multi_zone_subnet_policy": "EQUALITY", "name": "whatsapp-spot", "node_config": [], "node_count": 1, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-bb2394o9", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-51e1otso"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-qm4rn4zz", "wait_node_ready": null, "zones": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.eni_pod_subnet", "tencentcloud_subnet.public_whatsapp", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_nat_gateway", "name": "nat_gateway", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"assigned_eip_set": ["************"], "bandwidth": 100, "created_time": "2025-06-11 05:02:17", "id": "nat-841muwfq", "max_concurrent": 1000000, "name": "wall-nat", "nat_product_version": 1, "stock_public_ip_addresses_bandwidth_out": 1, "subnet_id": "", "tags": {}, "vpc_id": "vpc-qm4rn4zz", "zone": "sa-saopaulo-1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_eip.nat_eip", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_route_table", "name": "private_route_table", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"create_time": "2025-06-11 05:02:10", "id": "rtb-10v82bvk", "is_default": false, "name": "private-route-table", "route_entry_ids": ["181584.rtb-10v82bvk"], "subnet_ids": ["subnet-hdgyvnq6", "subnet-4fx3fmm0"], "tags": {}, "vpc_id": "vpc-qm4rn4zz"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_route_table_association", "name": "eni_pod_association", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"id": "subnet-4fx3fmm0", "route_table_id": "rtb-10v82bvk", "subnet_id": "subnet-4fx3fmm0"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_route_table.private_route_table", "tencentcloud_subnet.eni_pod_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_route_table_association", "name": "private_association", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"id": "subnet-hdgyvnq6", "route_table_id": "rtb-10v82bvk", "subnet_id": "subnet-hdgyvnq6"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_route_table.private_route_table", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_route_table_entry", "name": "internet_route", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"description": "", "destination_cidr_block": "0.0.0.0/0", "disabled": false, "id": "181584.rtb-10v82bvk", "next_hub": "nat-841muwfq", "next_type": "NAT", "route_item_id": "rti-bc9jjnm9", "route_table_id": "rtb-10v82bvk"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_eip.nat_eip", "tencentcloud_nat_gateway.nat_gateway", "tencentcloud_route_table.private_route_table", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_security_group", "name": "bastion_sg", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Security group for bastion host", "id": "sg-2jx9w8fx", "name": "bastion-sg", "project_id": 0, "tags": {"cluster": "wall", "name": "bastion-sg", "terraform": "true"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "tencentcloud_security_group", "name": "cluster_sg", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Security group for Kubernetes cluster", "id": "sg-9gseiv0b", "name": "wall-sg", "project_id": 0, "tags": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "allow_outbound", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTJqeDl3OGZ4IiwicG9saWN5X3R5cGUiOiJlZ3Jlc3MiLCJjaWRyX2lwIjoiMC4wLjAuMC8wIiwicHJvdG9jb2wiOiJhbGwiLCJwb3J0X3JhbmdlIjoiQUxMIiwiYWN0aW9uIjoiYWNjZXB0Iiwic291cmNlX3NnX2lkIjoiIiwiZGVzY3JpcHRpb24iOiIifQ==", "ip_protocol": "all", "policy": "accept", "policy_index": null, "port_range": "ALL", "protocol_template": [], "security_group_id": "sg-2jx9w8fx", "source_sgid": null, "type": "egress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.bastion_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "allow_ssh", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTJqeDl3OGZ4IiwicG9saWN5X3R5cGUiOiJpbmdyZXNzIiwiY2lkcl9pcCI6IjAuMC4wLjAvMCIsInByb3RvY29sIjoidGNwIiwicG9ydF9yYW5nZSI6IjIyIiwiYWN0aW9uIjoiYWNjZXB0Iiwic291cmNlX3NnX2lkIjoiIiwiZGVzY3JpcHRpb24iOiIifQ==", "ip_protocol": "tcp", "policy": "accept", "policy_index": null, "port_range": "22", "protocol_template": [], "security_group_id": "sg-2jx9w8fx", "source_sgid": null, "type": "ingress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.bastion_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_egress", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJlZ3Jlc3MiLCJjaWRyX2lwIjoiMC4wLjAuMC8wIiwicHJvdG9jb2wiOiJBTEwiLCJwb3J0X3JhbmdlIjoiQUxMIiwiYWN0aW9uIjoiQUNDRVBUIiwic291cmNlX3NnX2lkIjoiIiwiZGVzY3JpcHRpb24iOiIifQ==", "ip_protocol": "ALL", "policy": "ACCEPT", "policy_index": null, "port_range": "ALL", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "egress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_ingress_http", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJpbmdyZXNzIiwiY2lkcl9pcCI6IjAuMC4wLjAvMCIsInByb3RvY29sIjoiVENQIiwicG9ydF9yYW5nZSI6IjgwLDQ0MyIsImFjdGlvbiI6IkFDQ0VQVCIsInNvdXJjZV9zZ19pZCI6IiIsImRlc2NyaXB0aW9uIjoiIn0=", "ip_protocol": "TCP", "policy": "ACCEPT", "policy_index": null, "port_range": "80,443", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "ingress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_ingress_k8s_api", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJpbmdyZXNzIiwiY2lkcl9pcCI6IjAuMC4wLjAvMCIsInByb3RvY29sIjoiVENQIiwicG9ydF9yYW5nZSI6IjY0NDMiLCJhY3Rpb24iOiJBQ0NFUFQiLCJzb3VyY2Vfc2dfaWQiOiIiLCJkZXNjcmlwdGlvbiI6IiJ9", "ip_protocol": "TCP", "policy": "ACCEPT", "policy_index": null, "port_range": "6443", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "ingress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_ingress_ssh", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJpbmdyZXNzIiwiY2lkcl9pcCI6IjAuMC4wLjAvMCIsInByb3RvY29sIjoiVENQIiwicG9ydF9yYW5nZSI6IjIyIiwiYWN0aW9uIjoiQUNDRVBUIiwic291cmNlX3NnX2lkIjoiIiwiZGVzY3JpcHRpb24iOiIifQ==", "ip_protocol": "TCP", "policy": "ACCEPT", "policy_index": null, "port_range": "22", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "ingress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_ingress_vpc", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "10.0.0.0/16", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJpbmdyZXNzIiwiY2lkcl9pcCI6IjEwLjAuMC4wLzE2IiwicHJvdG9jb2wiOiJBTEwiLCJwb3J0X3JhbmdlIjoiQUxMIiwiYWN0aW9uIjoiQUNDRVBUIiwic291cmNlX3NnX2lkIjoiIiwiZGVzY3JpcHRpb24iOiIifQ==", "ip_protocol": "ALL", "policy": "ACCEPT", "policy_index": null, "port_range": "ALL", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "ingress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_subnet", "name": "eni_pod_subnet", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"availability_zone": "sa-saopaulo-1", "available_ip_count": 16364, "cdc_id": "", "cidr_block": "********/18", "create_time": "2025-06-11 05:02:11", "id": "subnet-4fx3fmm0", "is_default": false, "is_multicast": false, "name": "eni-pod-subnet", "route_table_id": "rtb-10v82bvk", "tags": {"cluster": "wall", "terraform": "true", "usage": "pod-ips"}, "vpc_id": "vpc-qm4rn4zz"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_subnet", "name": "private_subnet", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"availability_zone": "sa-saopaulo-1", "available_ip_count": 1017, "cdc_id": "", "cidr_block": "********/22", "create_time": "2025-06-11 05:02:11", "id": "subnet-hdgyvnq6", "is_default": false, "is_multicast": false, "name": "private-subnet", "route_table_id": "rtb-10v82bvk", "tags": {"cluster": "wall", "terraform": "true", "usage": "nodes"}, "vpc_id": "vpc-qm4rn4zz"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_subnet", "name": "public_subnet", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"availability_zone": "sa-saopaulo-1", "available_ip_count": 1020, "cdc_id": "", "cidr_block": "10.0.0.0/22", "create_time": "2025-06-11 05:02:11", "id": "subnet-gat724b6", "is_default": false, "is_multicast": false, "name": "public-subnet", "route_table_id": "rtb-byqswin8", "tags": {"cluster": "wall", "terraform": "true", "usage": "load-balancers"}, "vpc_id": "vpc-qm4rn4zz"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_subnet", "name": "public_whatsapp", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"availability_zone": "sa-saopaulo-1", "available_ip_count": 1020, "cdc_id": "", "cidr_block": "********/22", "create_time": "2025-06-11 05:02:09", "id": "subnet-51e1otso", "is_default": false, "is_multicast": false, "name": "public-whatsapp", "route_table_id": "rtb-byqswin8", "tags": {"cluster": "wall", "terraform": "true", "usage": "whatsapp"}, "vpc_id": "vpc-qm4rn4zz"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_vpc", "name": "k8s_vpc", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"assistant_cidrs": [], "cidr_block": "10.0.0.0/12", "create_time": "2025-06-11 05:02:04", "default_route_table_id": "rtb-byqswin8", "dns_servers": ["*******", "************", "************"], "docker_assistant_cidrs": ["**********/20"], "id": "vpc-qm4rn4zz", "is_default": false, "is_multicast": false, "name": "wall-vpc", "tags": {"cluster": "wall", "terraform": "true"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}], "check_results": null}