
# Bastion Security Group
resource "tencentcloud_security_group" "bastion_sg" {
  name        = "bastion-sg"
  description = "Security group for bastion host"

  tags = {
    cluster   = var.cluster_name
    name        = "bastion-sg"
    terraform   = "true"
  }
}

# Allow SSH access from outside
resource "tencentcloud_security_group_rule" "allow_ssh" {
  security_group_id = tencentcloud_security_group.bastion_sg.id
  type              = "ingress"
  cidr_ip           = "0.0.0.0/0"
  ip_protocol       = "tcp"
  port_range        = "22"
  policy            = "accept"
}

# Allow all outbound traffic
resource "tencentcloud_security_group_rule" "allow_outbound" {
  security_group_id = tencentcloud_security_group.bastion_sg.id
  type              = "egress"
  cidr_ip           = "0.0.0.0/0"
  ip_protocol       = "all"
  policy            = "accept"
}

  # Bastion Host Configuration
resource "tencentcloud_instance" "bastion" {
  instance_name              = "${var.cluster_name}-bastion"  # Match naming with your cluster
  availability_zone          = var.availability_zones[0]      # Use your existing AZ variable
  image_id                   = "img-22trbn9x"                 # Ubuntu 20.04 or your preferred image
  instance_type              = "S5.MEDIUM2"                   # Smaller instance is fine for proxy only
  system_disk_type           = "CLOUD_PREMIUM"
  system_disk_size           = 50
  vpc_id                     = tencentcloud_vpc.k8s_vpc.id    # Reference your K8s VPC
  subnet_id                  = tencentcloud_subnet.public_subnet.id  # Use existing public subnet
  allocate_public_ip         = true
  internet_charge_type       = "TRAFFIC_POSTPAID_BY_HOUR"
  internet_max_bandwidth_out = 10
  key_ids                    = [tencentcloud_key_pair.cluster_key.id]
  orderly_security_groups    = [tencentcloud_security_group.bastion_sg.id]

  tags = {
    terraform = "true"
    cluster   = var.cluster_name
    component = "bastion"
  }
}

# Output the bastion's public IP
output "bastion_public_ip" {
  description = "Public IP address of the bastion host"
  value       = tencentcloud_instance.bastion.public_ip
}
