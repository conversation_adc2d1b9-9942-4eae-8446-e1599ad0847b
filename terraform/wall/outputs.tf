output "cluster_id" {
  description = "ID of the Kubernetes cluster"
  value       = tencentcloud_kubernetes_cluster.k8s_cluster.id
}

output "cluster_kubeconfig" {
  description = "KUBECONFIG for the cluster"
  value       = tencentcloud_kubernetes_cluster.k8s_cluster.kube_config
  sensitive   = true
}

output "cluster_ca_certificate" {
  description = "CA certificate for the Kubernetes cluster"
  value       = tencentcloud_kubernetes_cluster.k8s_cluster.certification_authority
  sensitive   = true
}

output "vpc_id" {
  description = "ID of the VPC"
  value       = tencentcloud_vpc.k8s_vpc.id
}

output "public_subnet_id" {
  description = "ID of the public subnet"
  value       = tencentcloud_subnet.public_subnet.id
}

output "private_subnet_id" {
  description = "ID of the private subnet"
  value       = tencentcloud_subnet.private_subnet.id
}

output "public_whatsapp_subnet_id" {
  description = "ID of the WhatsApp public subnet"
  value       = tencentcloud_subnet.public_whatsapp.id
}

output "stateless_spot_node_pool_id" {
  description = "ID of the stateless spot node pool"
  value       = tencentcloud_kubernetes_node_pool.stateless_spot.id
}

output "stateless_fallback_node_pool_id" {
  description = "ID of the stateless fallback node pool"
  value       = tencentcloud_kubernetes_node_pool.stateless_fallback.id
}

output "stateful_node_pool_id" {
  description = "ID of the stateful node pool"
  value       = tencentcloud_kubernetes_node_pool.stateful.id
}

output "whatsapp_spot_node_pool_id" {
  description = "ID of the WhatsApp spot node pool"
  value       = tencentcloud_kubernetes_node_pool.whatsapp_spot.id
}

output "whatsapp_fallback_node_pool_id" {
  description = "ID of the WhatsApp fallback node pool"
  value       = tencentcloud_kubernetes_node_pool.whatsapp_fallback.id
}

output "tools_node_pool_id" {
  description = "ID of the tools node pool"
  value       = tencentcloud_kubernetes_node_pool.tools.id
}

output "nat_gateway_id" {
  description = "ID of the NAT Gateway"
  value       = tencentcloud_nat_gateway.nat_gateway.id
}

output "nat_eip_address" {
  description = "Public IP address of the NAT Gateway"
  value       = tencentcloud_eip.nat_eip.public_ip
}

output "cluster_internal_endpoint" {
  description = "Internal endpoint for Kubernetes API server"
  value       = tencentcloud_kubernetes_cluster_endpoint.cluster_endpoint.pgw_endpoint
}

output "kubectl_config_raw" {
  description = "Raw kubectl configuration from TencentCloud"
  value       = tencentcloud_kubernetes_cluster.k8s_cluster.kube_config_intranet
  sensitive   = true
}
