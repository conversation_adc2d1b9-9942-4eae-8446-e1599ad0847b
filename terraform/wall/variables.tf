variable "region" {
  description = "Tencent Cloud region"
  type        = string
  default     = "sa-saopaulo"
}

variable "availability_zones" {
  description = "Availability zones for resources"
  type        = list(string)
  default     = ["sa-saopaulo-1", "sa-saopaulo-2", "sa-saopaulo-3"]
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/12"
}

variable "service_cidr" {
  description = "CIDR block for Kubernetes services (required for VPC-CNI)"
  type        = string
  default     = "**********/20"
}

variable "public_subnet_cidr" {
  description = "CIDR block for public subnet (load balancers)"
  type        = string
  default     = "10.0.0.0/22"
}

variable "private_subnet_cidr" {
  description = "CIDR block for private subnet (most nodes)"
  type        = string
  default     = "********/22"
}

variable "public_whatsapp_cidr" {
  description = "CIDR block for public WhatsApp subnet"
  type        = string
  default     = "********/22"
}

variable "eni_pod_subnet_cidr" {
  description = "CIDR block for eni_pod_subnet subnet"
  type        = string
  default     = "********/18"
}

variable "cluster_name" {
  description = "Name of the Kubernetes cluster"
  type        = string
  default     = "tencent-k8s-cluster"
}

variable "cluster_version" {
  description = "Kubernetes version for the cluster"
  type        = string
  default     = "1.30.0"
}

variable "cluster_cidr" {
  description = "CIDR block for Kubernetes cluster pods"
  type        = string
  default     = "**********/16"
}

variable "node_instance_type" {
  description = "Instance type for all nodes (8 vCPU - 32GB RAM - Intel)"
  type        = string
  default     = "S5.2XLARGE16"
}

variable "node_disk_size" {
  description = "Disk size for nodes in GB"
  type        = number
  default     = 100
}

variable "node_disk_type" {
  description = "Disk type for nodes"
  type        = string
  default     = "CLOUD_PREMIUM"
}

variable "min_node_count" {
  description = "Minimum node count for autoscalable node pools"
  type        = number
  default     = 1
}

variable "max_node_count" {
  description = "Maximum node count for autoscalable node pools"
  type        = number
  default     = 5
}

variable "desired_node_count" {
  description = "Desired initial node count"
  type        = number
  default     = 1
}

variable "ssh_public_key_path" {
  description = "Path to the SSH public key file"
  type        = string
  default     = "~/.ssh/id_rsa.pub"
}

variable "secret_id" {
  description = "Secret ID"
  type        = string
}

variable "secret_key" {
  description = "Secret Key"
  type        = string
}