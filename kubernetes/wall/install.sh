#!/usr/bin/env bash

set -ex

which kubectl >/dev/null 2>&1 || { echo "Error: kubectl not found. Please install it first."; exit 1; }
which istioctl >/dev/null 2>&1 || { echo "Error: istioctl not found. Please install it first."; exit 1; }
which helm >/dev/null 2>&1 || { echo "Error: helm not found. Please install it first."; exit 1; }

# install istio
kubectl create namespace istio-system || true
istioctl install -f ./tools/istio/istio.yaml -y

# install reloader
helm repo add stakater https://stakater.github.io/stakater-charts
helm repo update
kubectl create namespace reloader || true
helm upgrade --install reloader stakater/reloader \
  -n reloader \
  -f ./tools/reloader/values.yaml

# install postgres
kubectl create namespace postgres || true
helm upgrade --install postgres ./tools/postgres \
  -n postgres \
  -f ./tools/postgres/values.yaml

# install pulsar
kubectl create namespace pulsar || true
helm repo add apache https://pulsar.apache.org/charts
helm repo update
helm upgrade --install pulsar apache/pulsar \
  -n pulsar \
  -f ./tools/pulsar/values.yaml

# install dekaf
helm upgrade --install dekaf ./tools/dekaf \
  -n pulsar \
  -f ./tools/dekaf/values.yaml

# install pgbouncer
kubectl create namespace pgbouncer || true
helm upgrade --install pgbouncer ./tools/pgbouncer \
  -n pgbouncer \
  -f "./tools/pgbouncer/values.yaml"

# install ocir-credentials
helm upgrade --install ocir-credentials ./tools/ocir-credentials \
  -f "./tools/ocir-credentials/values.yaml"

# install wall app (consolidated chart)
kubectl create namespace app || true
helm upgrade --install app ./app \
  -n app \
  -f ./app/values.yaml