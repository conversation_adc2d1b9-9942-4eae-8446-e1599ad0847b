namespace: app
# Registry credentials
registryCredentialName: ocirsecret

# Node Affinity Configuration
nodeAffinity:
  key: pool
  value:
    - stateful


# Message Broker Components
messageBrokerProducer:
  replicaCount: 1
  image:
    repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/message-broker/producer
    tag: "1.3.0"

  service:
    type: ClusterIP
    port: 2000

  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "200m"

  env:
    LOG_LEVEL: "debug"
    RABBITMQ_HOST: "lavinmq.tools.svc.cluster.local"

  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 2
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

messageBrokerConsumer:
  replicaCount: 1
  image:
    repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/message-broker/consumer
    tag: "1.3.0"

  resources:
    requests:
      memory: "256Mi"
      cpu: "200m"
    limits:
      memory: "512Mi"
      cpu: "500m"

  env:
    RABBITMQ_HOST: "lavinmq.tools.svc.cluster.local"

  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 3
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

statelessNodeAffinity:
  key: pool
  value:
    - stateless

whatsappNodeAffinity:
  key: pool
  value:
    - whatsapp

# API Component
api:
  replicaCount: 1
  image:
    repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/wall
    tag: api-v1.11.0
  
  service:
    type: LoadBalancer
    ports:
      http: 5060
      grpc1: 50060
      grpc2: 50062
      grpc3: 50063
  
  resources:
    requests:
      memory: "256Mi"
    limits:
      memory: "256Mi"
  
  env:
    API_ADDRESS: ":5060"
    SERVER_POD_API_GRPC_ADDRESS: ":50060"
    JOBS_GRPC_ADDRESS: ":50062"
    PUBLIC_GRPC_ADDRESS: ":50063"
    JWT_SECRET_KEY: "\\6RUtw)cX>P8o4*+J+Hp7i8J#'w.A]k~+l?6(1n\\:,pqPQA_+["
    DB_HOST: "postgres.postgres.svc.cluster.local"
    DB_PORT: "5432"
    DB_USERNAME: "postgres"
    DB_PASSWORD: "postgres"
    DB_NAME: "wall"
    DB_MAX_CONNS: "500"
    DB_LOG: "true"
    QUEUE_MANAGER_DISPATCHER_URL: "http://message-broker-producer:2000/dispatch"
    DEFAULT_WA_VERSION: "2.3000.1024969685"
    DEFAULT_WPP_CONNECT_VERSION: "3.17.7"
  
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 5
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80


# Cron Component
cron:
  replicaCount: 1
  image:
    repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/wall
    tag: cron-v1.11.0

  resources:
    requests:
      memory: "256Mi"
    limits:
      memory: "256Mi"

  env:
    JOBS_GRPC_ADDRESS: "api:50062"

  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 3
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

# Scripts Component
scripts:
  image:
    repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/wall
    tag: scripts-v1.11.0

  resources:
    requests:
      memory: "256Mi"
    limits:
      memory: "256Mi"

  env:
    API_ADDRESS: ":5060"
    SERVER_POD_API_GRPC_ADDRESS: ":50060"
    JOBS_GRPC_ADDRESS: ":50062"
    PUBLIC_GRPC_ADDRESS: ":50063"
    JWT_SECRET_KEY: "\\6RUtw)cX>P8o4*+J+Hp7i8J#'w.A]k~+l?6(1n\\:,pqPQA_+["
    DB_HOST: "postgres.postgres.svc.cluster.local"
    DB_PORT: "5432"
    DB_USERNAME: "postgres"
    DB_PASSWORD: "postgres"
    DB_NAME: "wall"
    DB_MAX_CONNS: "500"
    DB_LOG: "true"

# Autoscaler Component
autoscaler:
  replicaCount: 1
  image:
    repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/wall
    tag: autoscaler-v1.11.0

  service:
    type: ClusterIP
    port: 50064

  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "200m"

  env:
    GRPC_ADDRESS: ":50064"
    API_GRPC_ADDRESS: "api.app.svc.cluster.local:50060"
    LOG_TYPE: "text"
    DB_HOST: "postgres.postgres.svc.cluster.local"
    DB_PORT: "5432"
    DB_USERNAME: "postgres"
    DB_PASSWORD: "postgres"
    DB_NAME: "wall"
    DB_MAX_CONNS: "100"
    DB_LOG: "true"

  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 3
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  storage:
    size: 10Gi
    storageClass: ""

# WhatsApp Server Browser Component
whatsappServerBrowser:
  replicaCount: 1

  whatsappServer:
    image:
      repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/wall
      tag: serverpod-v1.11.0

    service:
      type: ClusterIP
      port: 50061

    resources:
      requests:
        memory: "256Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"

    env:
      INSTANCE_ID: "whatsapp-server-local-1"
      INSTANCE_TYPE: "default"
      PUBLIC_ADDRESS: "whatsapp-server.app.svc.cluster.local:50061"
      GRPC_ADDRESS: ":50061"
      API_GRPC_ADDRESS: "api.app.svc.cluster.local:50060"
      LOG_TYPE: "text"
      CHROME_ADDRESS: "ws://localhost:3000"
      AWS_REGION: "us-east-1"
      AWS_BUCKET_NAME: "mandeumzap-storage"
      AWS_ACCESS_KEY_ID: "AKIARYNLF2Q6NTLJPUUB"
      AWS_SECRET_ACCESS_KEY: "7Qj7PYP19evr530vTrJIKeIthtZ8Y4iBeO02FMO"
      WA_VERSION_TOKEN: "**************************"

  browserless:
    image:
      repository: "browserless/chrome"
      tag: "latest"

    resources:
      requests:
        memory: "512Mi"
        cpu: "500m"
      limits:
        memory: "2Gi"
        cpu: "1000m"

    config:
      concurrent: "100"
      timeout: "0"
      token: "6R0W53R135510"
      errorAlertUrl: ""
      enableCors: "true"
      enableWebSecurity: "false"
      disableWebSecurity: "true"

    storage:
      sessions:
        size: 100Gi
        storageClass: ""
      shm:
        sizeLimit: "2Gi"
