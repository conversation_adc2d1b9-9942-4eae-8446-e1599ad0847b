apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: autoscaler
  namespace: {{ .Values.namespace }}
  labels:
    app: autoscaler
    component: autoscaler
spec:
  serviceName: autoscaler
  replicas: {{ .Values.autoscaler.replicaCount }}
  selector:
    matchLabels:
      app: autoscaler
  template:
    metadata:
      labels:
        app: autoscaler
        component: autoscaler
    spec:
      containers:
        - name: autoscaler
          image: "{{ .Values.autoscaler.image.repository }}:{{ .Values.autoscaler.image.tag }}"
          imagePullPolicy: IfNotPresent
          ports:
            - name: grpc
              containerPort: {{ .Values.autoscaler.service.port }}
              protocol: TCP
          envFrom:
            - configMapRef:
                name: autoscaler-env
          resources:
            {{- toYaml .Values.autoscaler.resources | nindent 12 }}
          volumeMounts:
            - name: autoscaler-data
              mountPath: /data

      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}

  volumeClaimTemplates:
    - metadata:
        name: autoscaler-data
      spec:
        accessModes: [ "ReadWriteOnce" ]
        resources:
          requests:
            storage: {{ .Values.autoscaler.storage.size }}
        {{- if .Values.autoscaler.storage.storageClass }}
        storageClassName: {{ .Values.autoscaler.storage.storageClass }}
        {{- end }}
