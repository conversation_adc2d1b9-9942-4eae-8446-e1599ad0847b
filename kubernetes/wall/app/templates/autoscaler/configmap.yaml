apiVersion: v1
kind: ConfigMap
metadata:
  name: autoscaler-env
  namespace: {{ .Values.namespace }}
  labels:
    app: autoscaler
    component: autoscaler
data:
  GRPC_ADDRESS: {{ .Values.autoscaler.env.GRPC_ADDRESS | quote }}
  API_GRPC_ADDRESS: {{ .Values.autoscaler.env.API_GRPC_ADDRESS | quote }}
  LOG_TYPE: {{ .Values.autoscaler.env.LOG_TYPE | quote }}
  DB_HOST: {{ .Values.autoscaler.env.DB_HOST | quote }}
  DB_PORT: {{ .Values.autoscaler.env.DB_PORT | quote }}
  DB_USERNAME: {{ .Values.autoscaler.env.DB_USERNAME | quote }}
  DB_PASSWORD: {{ .Values.autoscaler.env.DB_PASSWORD | quote }}
  DB_NAME: {{ .Values.autoscaler.env.DB_NAME | quote }}
  DB_MAX_CONNS: {{ .Values.autoscaler.env.DB_MAX_CONNS | quote }}
  DB_LOG: {{ .Values.autoscaler.env.DB_LOG | quote }}
