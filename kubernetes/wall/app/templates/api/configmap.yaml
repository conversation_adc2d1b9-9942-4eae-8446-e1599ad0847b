apiVersion: v1
kind: ConfigMap
metadata:
  name: api-env
  namespace: {{ .Values.namespace }}
  labels:
    app: api
    component: api
data:
  API_ADDRESS: {{ .Values.api.env.API_ADDRESS | quote }}
  SERVER_POD_API_GRPC_ADDRESS: {{ .Values.api.env.SERVER_POD_API_GRPC_ADDRESS | quote }}
  JOBS_GRPC_ADDRESS: {{ .Values.api.env.JOBS_GRPC_ADDRESS | quote }}
  PUBLIC_GRPC_ADDRESS: {{ .Values.api.env.PUBLIC_GRPC_ADDRESS | quote }}
  JWT_SECRET_KEY: {{ .Values.api.env.JWT_SECRET_KEY | quote }}
  DB_HOST: {{ .Values.api.env.DB_HOST | quote }}
  DB_PORT: {{ .Values.api.env.DB_PORT | quote }}
  DB_USERNAME: {{ .Values.api.env.DB_USERNAME | quote }}
  DB_PASSWORD: {{ .Values.api.env.DB_PASSWORD | quote }}
  DB_NAME: {{ .Values.api.env.DB_NAME | quote }}
  DB_MAX_CONNS: {{ .Values.api.env.DB_MAX_CONNS | quote }}
  DB_LOG: {{ .Values.api.env.DB_LOG | quote }}
  QUEUE_MANAGER_DISPATCHER_URL: {{ .Values.api.env.QUEUE_MANAGER_DISPATCHER_URL | quote }}
  DEFAULT_WA_VERSION: {{ .Values.api.env.DEFAULT_WA_VERSION | quote }} 
  DEFAULT_WPP_CONNECT_VERSION: {{ .Values.api.env.DEFAULT_WPP_CONNECT_VERSION | quote }}
