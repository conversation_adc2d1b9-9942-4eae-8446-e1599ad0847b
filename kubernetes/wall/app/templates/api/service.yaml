apiVersion: v1
kind: Service
metadata:
  name: api
  namespace: {{ .Values.namespace }}
  labels:
    app: api
    component: api
  annotations:
    service.cloud.tencent.com/loadbalance-type: "OPEN"
    service.kubernetes.io/loadbalance-type: "OPEN"
spec:
  type: {{ .Values.api.service.type }}
  selector:
    app: api
  ports:
    - name: api-http
      port: {{ .Values.api.service.ports.http }}
      targetPort: {{ .Values.api.service.ports.http }}
    - name: api-grpc-1
      port: {{ .Values.api.service.ports.grpc1 }}
      targetPort: {{ .Values.api.service.ports.grpc1 }}
    - name: api-grpc-2
      port: {{ .Values.api.service.ports.grpc2 }}
      targetPort: {{ .Values.api.service.ports.grpc2 }}
    - name: api-grpc-3
      port: {{ .Values.api.service.ports.grpc3 }}
      targetPort: {{ .Values.api.service.ports.grpc3 }}