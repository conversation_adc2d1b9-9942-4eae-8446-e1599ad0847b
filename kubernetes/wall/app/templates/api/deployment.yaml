apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
  namespace: {{ .Values.namespace }}
  labels:
    app: api
    component: api
spec:
  replicas: {{ .Values.api.replicaCount }}
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
        component: api
    spec:
      containers:
        - name: api
          image: "{{ .Values.api.image.repository }}:{{ .Values.api.image.tag }}"
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: {{ .Values.api.service.ports.http }}
            - containerPort: {{ .Values.api.service.ports.grpc1 }}
            - containerPort: {{ .Values.api.service.ports.grpc2 }}
            - containerPort: {{ .Values.api.service.ports.grpc3 }}
          envFrom:
            - configMapRef:
                name: api-env
          resources:
            requests:
              memory: {{ .Values.api.resources.requests.memory | quote }}
            limits:
              memory: {{ .Values.api.resources.limits.memory | quote }}
      affinity:
        {{- if .Values.statelessNodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.statelessNodeAffinity.key }}
                operator: In
                values: {{ .Values.statelessNodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}

