apiVersion: apps/v1
kind: Deployment
metadata:
  name: message-broker-consumer
  namespace: {{ .Values.namespace }}
  labels:
    app: message-broker-consumer
    version: v1
spec:
  replicas: {{ .Values.messageBrokerConsumer.replicaCount }}
  selector:
    matchLabels:
      app: message-broker-consumer
      version: v1
  template:
    metadata:
      labels:
        app: message-broker-consumer
        version: v1
    spec:
      containers:
      - name: message-broker-consumer
        image: "{{ .Values.messageBrokerConsumer.image.repository }}:{{ .Values.messageBrokerConsumer.image.tag }}"
        env:
        - name: RABBITMQ_HOST
          value: {{ .Values.messageBrokerConsumer.env.RABBITMQ_HOST | quote }}
        volumeMounts:
        - name: queues-config
          mountPath: /app/config.yaml
          subPath: config.yaml
        resources:
          requests:
            memory: {{ .Values.messageBrokerConsumer.resources.requests.memory }}
            cpu: {{ .Values.messageBrokerConsumer.resources.requests.cpu }}
          limits:
            memory: {{ .Values.messageBrokerConsumer.resources.limits.memory }}
            cpu: {{ .Values.messageBrokerConsumer.resources.limits.cpu }}
      volumes:
      - name: queues-config
        configMap:
          name: message-broker-queues-config
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
