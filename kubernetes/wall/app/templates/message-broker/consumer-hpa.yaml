{{- if .Values.messageBrokerConsumer.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: message-broker-consumer-hpa
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: message-broker-consumer
  minReplicas: {{ .Values.messageBrokerConsumer.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.messageBrokerConsumer.autoscaling.maxReplicas }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ .Values.messageBrokerConsumer.autoscaling.targetCPUUtilizationPercentage }}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: {{ .Values.messageBrokerConsumer.autoscaling.targetMemoryUtilizationPercentage }}
{{- end }}
