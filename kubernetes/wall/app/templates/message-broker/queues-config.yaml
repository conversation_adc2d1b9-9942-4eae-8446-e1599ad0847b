apiVersion: v1
kind: ConfigMap
metadata:
  name: message-broker-queues-config
  namespace: {{ .Values.namespace }}
data:
  config.yaml: |
    defaults:
      max-retries: 25
      processing-timeout-seconds: 60
      mode: concurrent
      consumers-per-instance: 10 # for mode:concurrent
      partitions: 10 # for mode:sequential
      handler: main-workers

    handlers:
      wall-api:
        url: http://api.app.svc.cluster.local:5060/:queueName

    queues:
      send-messages:
        mode: sequential
        handler: wall-api
        max-retries: 7 
        topics:
          - 'send-messages'

      forward-messages:
        mode: sequential
        handler: wall-api
        max-retries: 7
        topics:
          - 'forward-messages'

      webhooks:
        mode: sequential
        handler: wall-api
        max-retries: 7
        topics:
          - 'webhooks'
