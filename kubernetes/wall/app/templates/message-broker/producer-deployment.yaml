apiVersion: apps/v1
kind: Deployment
metadata:
  name: message-broker-producer
  namespace: {{ .Values.namespace }}
  labels:
    app: message-broker-producer
    version: v1
spec:
  replicas: {{ .Values.messageBrokerProducer.replicaCount }}
  selector:
    matchLabels:
      app: message-broker-producer
      version: v1
  template:
    metadata:
      labels:
        app: message-broker-producer
        version: v1
    spec:
      containers:
      - name: message-broker-producer
        image: "{{ .Values.messageBrokerProducer.image.repository }}:{{ .Values.messageBrokerProducer.image.tag }}"
        ports:
        - containerPort: {{ .Values.messageBrokerProducer.service.port }}
        env:
        - name: LOG_LEVEL
          value: {{ .Values.messageBrokerProducer.env.LOG_LEVEL | quote }}
        - name: RABBITMQ_HOST
          value: {{ .Values.messageBrokerProducer.env.RABBITMQ_HOST | quote }}
        resources:
          requests:
            memory: {{ .Values.messageBrokerProducer.resources.requests.memory }}
            cpu: {{ .Values.messageBrokerProducer.resources.requests.cpu }}
          limits:
            memory: {{ .Values.messageBrokerProducer.resources.limits.memory }}
            cpu: {{ .Values.messageBrokerProducer.resources.limits.cpu }}
        # livenessProbe:
        #   httpGet:
        #     path: /health
        #     port: 2000
        #   initialDelaySeconds: 30
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 3
        # readinessProbe:
        #   httpGet:
        #     path: /health
        #     port: 2000
        #   initialDelaySeconds: 5
        #   periodSeconds: 5
        #   timeoutSeconds: 3
        #   failureThreshold: 3
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
