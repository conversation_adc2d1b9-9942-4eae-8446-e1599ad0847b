apiVersion: v1
kind: Service
metadata:
  name: message-broker-producer
  namespace: {{ .Values.namespace }}
  labels:
    app: message-broker-producer
    service: message-broker-producer
spec:
  type: {{ .Values.messageBrokerProducer.service.type }}
  ports:
  - port: {{ .Values.messageBrokerProducer.service.port }}
    targetPort: {{ .Values.messageBrokerProducer.service.port }}
    name: http
  selector:
    app: message-broker-producer
