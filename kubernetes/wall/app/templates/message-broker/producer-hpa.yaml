{{- if .Values.messageBrokerProducer.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: message-broker-producer-hpa
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: message-broker-producer
  minReplicas: {{ .Values.messageBrokerProducer.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.messageBrokerProducer.autoscaling.maxReplicas }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ .Values.messageBrokerProducer.autoscaling.targetCPUUtilizationPercentage }}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: {{ .Values.messageBrokerProducer.autoscaling.targetMemoryUtilizationPercentage }}
{{- end }}
