apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cron
  namespace: {{ .Values.namespace }}
  labels:
    app: cron
    component: cron
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cron
  minReplicas: {{ .Values.cron.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.cron.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.cron.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.cron.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.cron.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.cron.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}