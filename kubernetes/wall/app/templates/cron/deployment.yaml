apiVersion: apps/v1
kind: Deployment
metadata:
  name: cron
  namespace: {{ .Values.namespace }}
  labels:
    app: cron
    component: cron
spec:
  replicas: {{ .Values.cron.replicaCount }}
  selector:
    matchLabels:
      app: cron
  template:
    metadata:
      labels:
        app: cron
        component: cron
    spec:
      containers:
        - name: cron
          image: "{{ .Values.cron.image.repository }}:{{ .Values.cron.image.tag }}"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: cron-env
          resources:
            requests:
              memory: {{ .Values.cron.resources.requests.memory }}
            limits:
              memory: {{ .Values.cron.resources.limits.memory }}
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}