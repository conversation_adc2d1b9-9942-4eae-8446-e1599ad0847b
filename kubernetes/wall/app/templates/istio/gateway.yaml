apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: wall-gateway
  namespace: {{ .Values.namespace }}
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*"
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: wall-tls-secret
    hosts:
    - "*"
  - port:
      number: 8063
      name: grpc-public
      protocol: HTTP2
    hosts:
    - "*"
  - port:
      number: 8062
      name: grpc-jobs
      protocol: HTTP2
    hosts:
    - "*"
  - port:
      number: 8060
      name: grpc-server
      protocol: HTTP2
    hosts:
    - "*"
  - port:
      number: 3000
      name: browserless
      protocol: HTTP
    hosts:
    - "*"
