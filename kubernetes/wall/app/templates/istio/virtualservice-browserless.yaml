apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: browserless-path-vs
  namespace: {{ .Values.namespace }}
spec:
  hosts:
  - "*"
  gateways:
  - wall-gateway
  http:
  # Rota para /browserless (compatibilidade)
  - match:
    - uri:
        prefix: "/browserless"
    rewrite:
      uri: "/"
    route:
    - destination:
        host: whatsapp-server.{{ .Values.namespace }}.svc.cluster.local
        port:
          number: 3000
    timeout: 3600s
    headers:
      request:
        set:
          Upgrade: websocket
          Connection: upgrade
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: browserless-port-vs
  namespace: {{ .Values.namespace }}
spec:
  hosts:
  - "*"
  gateways:
  - wall-gateway
  tcp:
  # Rota direta para porta 3000
  - match:
    - port: 3000
    route:
    - destination:
        host: whatsapp-server.{{ .Values.namespace }}.svc.cluster.local
        port:
          number: 3000
