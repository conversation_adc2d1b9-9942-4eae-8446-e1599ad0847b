apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: grpc-vs
  namespace: {{ .Values.namespace }}
spec:
  hosts:
  - "*"
  gateways:
  - wall-gateway
  http:
  # gRPC Public (porta 8063)
  - match:
    - headers:
        ":authority":
          regex: ".*:8063"
    route:
    - destination:
        host: api.app.svc.cluster.local
        port:
          number: 50063
    timeout: 3600s
  # gRPC Jobs (porta 8062)
  - match:
    - headers:
        ":authority":
          regex: ".*:8062"
    route:
    - destination:
        host: api.app.svc.cluster.local
        port:
          number: 50062
    timeout: 3600s
  # gRPC Server (porta 8060)
  - match:
    - headers:
        ":authority":
          regex: ".*:8060"
    route:
    - destination:
        host: api.app.svc.cluster.local
        port:
          number: 50060
    timeout: 3600s
