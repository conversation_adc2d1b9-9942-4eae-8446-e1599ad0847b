apiVersion: v1
kind: Service
metadata:
  name: istio-ingressgateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    istio: ingressgateway
  annotations:
    service.cloud.tencent.com/exist-lbid: "lb-rjk7wbgb"
    service.cloud.tencent.com/loadbalance-type: "OPEN"
    service.kubernetes.io/loadbalance-type: "OPEN"
    external-dns.alpha.kubernetes.io/hostname: "lb-rjk7wbgb-13o5uzv42q0rki7g.clb.sao-tencentclb.com"
spec:
  type: LoadBalancer
  loadBalancerIP: ""
  ports:
  - name: status-port
    port: 15021
    targetPort: 15021
    protocol: TCP
  - name: http2
    port: 80
    targetPort: 8080
    protocol: TCP
  - name: https
    port: 443
    targetPort: 8443
    protocol: TCP
  - name: grpc-public
    port: 8063
    targetPort: 8080
    protocol: TCP
  - name: grpc-jobs
    port: 8062
    targetPort: 8080
    protocol: TCP
  - name: grpc-server
    port: 8060
    targetPort: 8080
    protocol: TCP
  - name: browserless
    port: 3000
    targetPort: 8080
    protocol: TCP
  selector:
    app: istio-ingressgateway
    istio: ingressgateway
