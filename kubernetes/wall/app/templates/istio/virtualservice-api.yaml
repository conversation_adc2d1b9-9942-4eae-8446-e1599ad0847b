apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: api-http-vs
  namespace: {{ .Values.namespace }}
spec:
  hosts:
  - "*"
  gateways:
  - wall-gateway
  http:
  # HTTP API (porta 80)
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: api.app.svc.cluster.local
        port:
          number: 5060
    timeout: 3600s
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: api-grpc-vs
  namespace: {{ .Values.namespace }}
spec:
  hosts:
  - "*"
  gateways:
  - wall-gateway
  tcp:
  # gRPC Public (porta 8063)
  - match:
    - port: 8063
    route:
    - destination:
        host: api.app.svc.cluster.local
        port:
          number: 50063
  # gRPC Jobs (porta 8062)
  - match:
    - port: 8062
    route:
    - destination:
        host: api.app.svc.cluster.local
        port:
          number: 50062
  # gRPC Server (porta 8060)
  - match:
    - port: 8060
    route:
    - destination:
        host: api.app.svc.cluster.local
        port:
          number: 50060
