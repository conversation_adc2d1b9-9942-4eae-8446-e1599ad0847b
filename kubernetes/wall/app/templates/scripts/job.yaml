apiVersion: batch/v1
kind: Job
metadata:
  name: scripts
  namespace: {{ .Values.namespace }}
  labels:
    app: scripts
    component: scripts
spec:
  template:
    spec:
      containers:
        - name: scripts
          image: "{{ .Values.scripts.image.repository }}:{{ .Values.scripts.image.tag }}"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: scripts-env
          resources:
            requests:
              memory: {{ .Values.scripts.resources.requests.memory }}
            limits:
              memory: {{ .Values.scripts.resources.limits.memory }}
          command: ["sleep", "infinity"]
      affinity:
        {{- if .Values.statelessNodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.statelessNodeAffinity.key }}
                operator: In
                values: {{ .Values.statelessNodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
      restartPolicy: Never
