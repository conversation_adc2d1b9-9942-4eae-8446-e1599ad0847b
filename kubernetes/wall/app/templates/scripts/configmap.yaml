apiVersion: v1
kind: ConfigMap
metadata:
  name: scripts-env
  namespace: {{ .Values.namespace }}
  labels:
    app: scripts
    component: scripts
data:
  API_ADDRESS: {{ .Values.scripts.env.API_ADDRESS | quote }}
  SERVER_POD_API_GRPC_ADDRESS: {{ .Values.scripts.env.SERVER_POD_API_GRPC_ADDRESS | quote }}
  JOBS_GRPC_ADDRESS: {{ .Values.scripts.env.JOBS_GRPC_ADDRESS | quote }}
  PUBLIC_GRPC_ADDRESS: {{ .Values.scripts.env.PUBLIC_GRPC_ADDRESS | quote }}
  JWT_SECRET_KEY: {{ .Values.scripts.env.JWT_SECRET_KEY | quote }}
  DB_HOST: {{ .Values.scripts.env.DB_HOST | quote }}
  DB_PORT: {{ .Values.scripts.env.DB_PORT | quote }}
  DB_USERNAME: {{ .Values.scripts.env.DB_USERNAME | quote }}
  DB_PASSWORD: {{ .Values.scripts.env.DB_PASSWORD | quote }}
  DB_NAME: {{ .Values.scripts.env.DB_NAME | quote }}
  DB_MAX_CONNS: {{ .Values.scripts.env.DB_MAX_CONNS | quote }}
  DB_LOG: {{ .Values.scripts.env.DB_LOG | quote }}
