apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: whatsapp-server
  namespace: {{ .Values.namespace }}
  labels:
    app: whatsapp-server
    component: whatsapp-server-browser
spec:
  serviceName: whatsapp-server
  replicas: {{ .Values.whatsappServerBrowser.replicaCount }}
  selector:
    matchLabels:
      app: whatsapp-server
  template:
    metadata:
      labels:
        app: whatsapp-server
        component: whatsapp-server-browser
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "2112"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        runAsUser: 0
        runAsGroup: 0
      hostAliases:
        - ip: "127.0.0.1"
          hostnames:
            - "crashlogs.whatsapp.net"
      containers:
        - name: whatsapp-server
          image: "{{ .Values.whatsappServerBrowser.whatsappServer.image.repository }}:{{ .Values.whatsappServerBrowser.whatsappServer.image.tag }}"
          imagePullPolicy: IfNotPresent
          ports:
            - name: grpc
              containerPort: {{ .Values.whatsappServerBrowser.whatsappServer.service.port }}
              protocol: TCP
          envFrom:
            - configMapRef:
                name: whatsapp-server-env
          resources:
            {{- toYaml .Values.whatsappServerBrowser.whatsappServer.resources | nindent 12 }}

        - name: browserless
          image: "{{ .Values.whatsappServerBrowser.browserless.image.repository }}:{{ .Values.whatsappServerBrowser.browserless.image.tag }}"
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "-c", "find /usr/src/app/wp-sessions -type f -name 'SingletonLock' -delete && ./start.sh"]
          env:
            - name: CONCURRENT
              value: {{ .Values.whatsappServerBrowser.browserless.config.concurrent | quote }}
            - name: TIMEOUT
              value: {{ .Values.whatsappServerBrowser.browserless.config.timeout | quote }}
            - name: CONNECTION_TIMEOUT
              value: "300000"
            - name: TOKEN
              value: {{ .Values.whatsappServerBrowser.browserless.config.token | quote }}
            - name: ERROR_ALERT_URL
              value: {{ .Values.whatsappServerBrowser.browserless.config.errorAlertUrl | quote }}
            - name: DEFAULT_LAUNCH_ARGS
              value: '["--no-sandbox","--disable-web-security","--disable-features=VizDisplayCompositor","--disable-extensions","--disable-plugins","--disable-gpu","--disable-dev-shm-usage","--no-first-run","--disable-default-apps","--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"]'
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
          resources:
            {{- toYaml .Values.whatsappServerBrowser.browserless.resources | nindent 12 }}
          volumeMounts:
            - name: browserless-sessions
              mountPath: /usr/src/app/wp-sessions
            - name: dshm
              mountPath: /dev/shm

      affinity:
        {{- if .Values.whatsappNodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.whatsappNodeAffinity.key }}
                operator: In
                values: {{ .Values.whatsappNodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
      volumes:
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: {{ .Values.whatsappServerBrowser.browserless.storage.shm.sizeLimit }}

  volumeClaimTemplates:
    - metadata:
        name: browserless-sessions
      spec:
        accessModes: [ "ReadWriteOnce" ]
        resources:
          requests:
            storage: {{ .Values.whatsappServerBrowser.browserless.storage.sessions.size }}
        {{- if .Values.whatsappServerBrowser.browserless.storage.sessions.storageClass }}
        storageClassName: {{ .Values.whatsappServerBrowser.browserless.storage.sessions.storageClass }}
        {{- end }}