{{/*apiVersion: autoscaling/v2*/}}
{{/*kind: HorizontalPodAutoscaler*/}}
{{/*metadata:*/}}
{{/*  name: whatsapp-server-hpa*/}}
{{/*  namespace: {{ .Values.namespace }}*/}}
{{/*  labels:*/}}
{{/*    app: whatsapp-server*/}}
{{/*    component: whatsapp-server-browser*/}}
{{/*spec:*/}}
{{/*  scaleTargetRef:*/}}
{{/*    apiVersion: apps/v1*/}}
{{/*    kind: StatefulSet*/}}
{{/*    name: whatsapp-server*/}}
{{/*  minReplicas: 1*/}}
{{/*  maxReplicas: 10*/}}
{{/*  metrics:*/}}
{{/*  - type: Prometheus*/}}
{{/*    prometheus:*/}}
{{/*      metric:*/}}
{{/*        name: whatsapp_available_slots*/}}
{{/*      target:*/}}
{{/*        type: AverageValue*/}}
{{/*        averageValue: 50  # Target 50 available slots per pod*/}}
