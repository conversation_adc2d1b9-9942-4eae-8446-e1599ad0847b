apiVersion: v1
kind: ConfigMap
metadata:
  name: whatsapp-server-env
  namespace: {{ .Values.namespace }}
  labels:
    app: whatsapp-server
    component: whatsapp-server-browser
data:
  INSTANCE_ID: {{ .Values.whatsappServerBrowser.whatsappServer.env.INSTANCE_ID | quote }}
  INSTANCE_TYPE: {{ .Values.whatsappServerBrowser.whatsappServer.env.INSTANCE_TYPE | quote }}
  PUBLIC_ADDRESS: {{ .Values.whatsappServerBrowser.whatsappServer.env.PUBLIC_ADDRESS | quote }}
  GRPC_ADDRESS: {{ .Values.whatsappServerBrowser.whatsappServer.env.GRPC_ADDRESS | quote }}
  API_GRPC_ADDRESS: {{ .Values.whatsappServerBrowser.whatsappServer.env.API_GRPC_ADDRESS | quote }}
  LOG_TYPE: {{ .Values.whatsappServerBrowser.whatsappServer.env.LOG_TYPE | quote }}
  CHROME_ADDRESS: {{ .Values.whatsappServerBrowser.whatsappServer.env.CHROME_ADDRESS | quote }}
  WA_VERSION_TOKEN: {{ .Values.whatsappServerBrowser.whatsappServer.env.WA_VERSION_TOKEN | quote }}
  AWS_REGION: {{ .Values.whatsappServerBrowser.whatsappServer.env.AWS_REGION | quote }}
  AWS_BUCKET_NAME: {{ .Values.whatsappServerBrowser.whatsappServer.env.AWS_BUCKET_NAME | quote }}
  AWS_ACCESS_KEY_ID: {{ .Values.whatsappServerBrowser.whatsappServer.env.AWS_ACCESS_KEY_ID | quote }}
  AWS_SECRET_ACCESS_KEY: {{ .Values.whatsappServerBrowser.whatsappServer.env.AWS_SECRET_ACCESS_KEY | quote }}
