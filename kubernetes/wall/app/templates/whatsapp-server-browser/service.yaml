apiVersion: v1
kind: Service
metadata:
  name: whatsapp-server
  namespace: {{ .Values.namespace }}
  labels:
    app: whatsapp-server
    component: whatsapp-server-browser
spec:
  type: {{ .Values.whatsappServerBrowser.whatsappServer.service.type }}
  ports:
    - name: grpc
      port: {{ .Values.whatsappServerBrowser.whatsappServer.service.port }}
      targetPort: {{ .Values.whatsappServerBrowser.whatsappServer.service.port }}
      protocol: TCP
    - name: browserless
      port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: whatsapp-server
