{{- if .Values.config.persistence.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
spec:
  accessModes:
    - {{ .Values.config.persistence.accessMode }}
  {{- if .Values.config.persistence.storageClass }}
  storageClassName: "{{ .Values.config.persistence.storageClass }}"
  {{- end }}
  resources:
    requests:
      storage: {{ .Values.config.persistence.size | quote }}
{{- end }}
