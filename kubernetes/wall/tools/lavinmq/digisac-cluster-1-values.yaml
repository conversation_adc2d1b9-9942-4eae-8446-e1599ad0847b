namespace: tools

image:
  repository: cloudamqp/lavinmq
  tag: "1.2.2"
  pullPolicy: IfNotPresent

replicaCount: 1

service:
  type: ClusterIP
  ports:
    amqp: 5672
    management: 15672

resources:
  requests:
    memory: "512Mi"
    cpu: "200m"
  limits:
    memory: "1Gi"
    cpu: "500m"

persistence:
  enabled: true
  storageClass: "cbs"
  size: 10Gi
  accessMode: ReadWriteOnce

env:
  LAVINMQ_DEFAULT_USER: "guest"
  LAVINMQ_DEFAULT_PASS: "guest"

nodeSelector:
  pool: tools

tolerations:
  - key: "pool"
    operator: "Equal"
    value: "tools"
    effect: "NoSchedule"

healthcheck:
  enabled: true
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 5
