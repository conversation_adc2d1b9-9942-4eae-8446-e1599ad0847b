namespace: tools

nginx:
  image:
    repository: nginx
    tag: "1.25-alpine"
    pullPolicy: IfNotPresent
  
  replicaCount: 2
  
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "200m"

service:
  type: LoadBalancer
  annotations:
    service.cloud.tencent.com/loadbalance-type: "OPEN"
    service.kubernetes.io/loadbalance-type: "OPEN"
  ports:
    http: 80
    grpc_public: 8063
    grpc_jobs: 8062
    grpc_server: 8060
    browserless: 3000

# Configuração dos backends (serviços da API no namespace app)
backends:
  api:
    namespace: app
    service: api
    ports:
      http: 5060
      grpc_public: 50063
      grpc_jobs: 50062
      grpc_server: 50060
  whatsapp:
    namespace: app
    service: whatsapp-server
    ports:
      browserless: 3000

nodeSelector:
  pool: tools

tolerations:
  - key: "pool"
    operator: "Equal"
    value: "tools"
    effect: "NoSchedule"
