apiVersion: v1
kind: Service
metadata:
  name: nginx-grpc-proxy
  namespace: {{ .Values.namespace }}
  labels:
    app: nginx-grpc-proxy
  {{- if .Values.service.annotations }}
  annotations:
    {{- toYaml .Values.service.annotations | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  selector:
    app: nginx-grpc-proxy
  ports:
  - name: http
    port: {{ .Values.service.ports.http }}
    targetPort: {{ .Values.service.ports.http }}
    protocol: TCP
  - name: grpc-public
    port: {{ .Values.service.ports.grpc_public }}
    targetPort: {{ .Values.service.ports.grpc_public }}
    protocol: TCP
  - name: grpc-jobs
    port: {{ .Values.service.ports.grpc_jobs }}
    targetPort: {{ .Values.service.ports.grpc_jobs }}
    protocol: TCP
  - name: grpc-server
    port: {{ .Values.service.ports.grpc_server }}
    targetPort: {{ .Values.service.ports.grpc_server }}
    protocol: TCP
  - name: browserless
    port: {{ .Values.service.ports.browserless }}
    targetPort: {{ .Values.service.ports.browserless }}
    protocol: TCP
