apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-grpc-proxy-config
  namespace: {{ .Values.namespace }}
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log;
        
        # Upstream para gRPC Public (50063)
        upstream grpc_public_backend {
            server {{ .Values.backends.api.service }}.{{ .Values.backends.api.namespace }}.svc.cluster.local:{{ .Values.backends.api.ports.grpc_public }};
        }
        
        # Upstream para gRPC Jobs (50062)
        upstream grpc_jobs_backend {
            server {{ .Values.backends.api.service }}.{{ .Values.backends.api.namespace }}.svc.cluster.local:{{ .Values.backends.api.ports.grpc_jobs }};
        }
        
        # Upstream para gRPC Server (50060)
        upstream grpc_server_backend {
            server {{ .Values.backends.api.service }}.{{ .Values.backends.api.namespace }}.svc.cluster.local:{{ .Values.backends.api.ports.grpc_server }};
        }
        
        # Upstream para HTTP API (5060)
        upstream http_backend {
            server {{ .Values.backends.api.service }}.{{ .Values.backends.api.namespace }}.svc.cluster.local:{{ .Values.backends.api.ports.http }};
        }

        # Upstream para Browserless (3000)
        upstream browserless_backend {
            server {{ .Values.backends.whatsapp.service }}.{{ .Values.backends.whatsapp.namespace }}.svc.cluster.local:{{ .Values.backends.whatsapp.ports.browserless }};
        }
        
        # HTTP API Proxy (porta 80)
        server {
            listen {{ .Values.service.ports.http }};
            server_name _;
            
            location / {
                proxy_pass http://http_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 10s;
                proxy_send_timeout 10s;
                proxy_read_timeout 10s;
            }
            
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
        }
        
        # gRPC Public Service (porta 8063 -> 50063)
        server {
            listen {{ .Values.service.ports.grpc_public }} http2;
            server_name _;
            
            location / {
                grpc_pass grpc://grpc_public_backend;
                grpc_set_header Host $host;
                grpc_set_header X-Real-IP $remote_addr;
                grpc_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                grpc_set_header X-Forwarded-Proto $scheme;
                grpc_connect_timeout 10s;
                grpc_send_timeout 10s;
                grpc_read_timeout 10s;
            }
        }
        
        # gRPC Jobs Service (porta 8062 -> 50062)
        server {
            listen {{ .Values.service.ports.grpc_jobs }} http2;
            server_name _;
            
            location / {
                grpc_pass grpc://grpc_jobs_backend;
                grpc_set_header Host $host;
                grpc_set_header X-Real-IP $remote_addr;
                grpc_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                grpc_set_header X-Forwarded-Proto $scheme;
                grpc_connect_timeout 10s;
                grpc_send_timeout 10s;
                grpc_read_timeout 10s;
            }
        }
        
        # gRPC Server Pod Service (porta 8060 -> 50060)
        server {
            listen {{ .Values.service.ports.grpc_server }} http2;
            server_name _;

            location / {
                grpc_pass grpc://grpc_server_backend;
                grpc_set_header Host $host;
                grpc_set_header X-Real-IP $remote_addr;
                grpc_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                grpc_set_header X-Forwarded-Proto $scheme;
                grpc_connect_timeout 10s;
                grpc_send_timeout 10s;
                grpc_read_timeout 10s;
            }
        }

        # Browserless Service (porta 3000)
        server {
            listen {{ .Values.service.ports.browserless }};
            server_name _;

            location / {
                proxy_pass http://browserless_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 10s;
                proxy_send_timeout 10s;
                proxy_read_timeout 10s;

                # WebSocket support for Browserless
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }
        }
    }
