apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-grpc-proxy
  namespace: {{ .Values.namespace }}
  labels:
    app: nginx-grpc-proxy
spec:
  replicas: {{ .Values.nginx.replicaCount }}
  selector:
    matchLabels:
      app: nginx-grpc-proxy
  template:
    metadata:
      labels:
        app: nginx-grpc-proxy
    spec:
      containers:
      - name: nginx
        image: {{ .Values.nginx.image.repository }}:{{ .Values.nginx.image.tag }}
        imagePullPolicy: {{ .Values.nginx.image.pullPolicy }}
        ports:
        - containerPort: {{ .Values.service.ports.http }}
          name: http
        - containerPort: {{ .Values.service.ports.grpc_public }}
          name: grpc-public
        - containerPort: {{ .Values.service.ports.grpc_jobs }}
          name: grpc-jobs
        - containerPort: {{ .Values.service.ports.grpc_server }}
          name: grpc-server
        - containerPort: {{ .Values.service.ports.browserless }}
          name: browserless
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        resources:
          requests:
            memory: {{ .Values.nginx.resources.requests.memory }}
            cpu: {{ .Values.nginx.resources.requests.cpu }}
          limits:
            memory: {{ .Values.nginx.resources.limits.memory }}
            cpu: {{ .Values.nginx.resources.limits.cpu }}
        livenessProbe:
          httpGet:
            path: /health
            port: {{ .Values.service.ports.http }}
          initialDelaySeconds: 10
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: {{ .Values.service.ports.http }}
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-grpc-proxy-config
      {{- if .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml .Values.nodeSelector | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations:
        {{- toYaml .Values.tolerations | nindent 8 }}
      {{- end }}
