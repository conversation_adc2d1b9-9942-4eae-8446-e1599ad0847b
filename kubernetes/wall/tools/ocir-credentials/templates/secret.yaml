apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.secretName }}
  namespace: {{ .Values.namespace }}
type: kubernetes.io/dockerconfigjson
stringData:
  .dockerconfigjson: |
    {
      "auths": {
        "{{ .Values.region }}.ocir.io": {
          "username": "{{ .Values.tenancyNamespace }}/{{ .Values.username }}",
          "password": "{{ .Values.authToken }}",
          "email": "{{ .Values.email }}",
          "auth": "{{ printf "%s/%s:%s" .Values.tenancyNamespace .Values.username .Values.authToken | b64enc }}"
        }
      }
    }