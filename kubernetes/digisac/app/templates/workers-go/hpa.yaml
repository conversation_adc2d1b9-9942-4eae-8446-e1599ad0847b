apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-workers-go-hpa
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-workers-go
  minReplicas: {{ .Values.autoscaler.workersGo.min }}
  maxReplicas: {{ .Values.autoscaler.workersGo.max }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
