apiVersion: v1
kind: ConfigMap
metadata:
  name: app-workers-go-config-envs
  namespace: app
data:
  GO_ENV: "{{ .Values.workersGoEnvs.goEnv }}"
  BRANCH: "{{ .Values.workersGoEnvs.branch }}"
  DEPLOYMENT: "{{ .Values.workersGoEnvs.deployment }}"
  PUBLIC_URL: "{{ .Values.workersGoEnvs.publicUrl }}"
  WHATSAPP_API_HTTP_ADDRESS: "{{ .Values.workersGoEnvs.whatsappApiHttpAddress }}"
  WHATSAPP_API_GRPC_ADDRESS: "{{ .Values.workersGoEnvs.whatsappApiGrpcAddress }}"
  WHATSAPP_API_TOKEN: "{{ .Values.workersGoEnvs.whatsappApiToken }}"
  WORKERS_GO_PORT: "{{ .Values.workersGoEnvs.workersGoPort }}"
  DB_HOST: "{{ .Values.workersGoEnvs.dbHost }}"
  DB_USERNAME: "{{ .Values.workersGoEnvs.dbUsername }}"
  DB_PASSWORD: "{{ .Values.workersGoEnvs.dbPassword }}"
  DB_NAME: "{{ .Values.workersGoEnvs.dbName }}"
  DB_PORT: "{{ .Values.workersGoEnvs.dbPort }}"
  DB_MAX_CONNS: "{{ .Values.workersGoEnvs.dbMaxConns }}"
  DB_LOG: "{{ .Values.workersGoEnvs.dbLog }}"
  LOG_TYPE: "{{ .Values.workersGoEnvs.logType }}"
  ENCRYPTION_KEY: "{{ .Values.workersGoEnvs.encryptionKey }}"
  STORAGE_DRIVER: "{{ .Values.workersGoEnvs.storageDriver }}"
  AWS_ACCESS_KEY_ID: "{{ .Values.workersGoEnvs.awsAccessKeyId }}"
  AWS_SECRET_ACCESS_KEY: "{{ .Values.workersGoEnvs.awsSecretAccessKey }}"
  AWS_BUCKET_NAME: "{{ .Values.workersGoEnvs.awsBucketName }}"
  AWS_REGION: "{{ .Values.workersGoEnvs.awsRegion }}"
  ORACLE_ACCESS_KEY_ID: "{{ .Values.workersGoEnvs.oracleAccessKeyId }}"
  ORACLE_SECRET_ACCESS_KEY: "{{ .Values.workersGoEnvs.oracleSecretAccessKey }}"
  ORACLE_BUCKETS_NAMES: "{{ .Values.workersGoEnvs.oracleBucketsNames }}"
  ORACLE_REGION: "{{ .Values.workersGoEnvs.oracleRegion }}"
  ORACLE_ENDPOINT: "{{ .Values.workersGoEnvs.oracleEndpoint }}"
  GUPSHUP_EMAIL: "{{ .Values.workersGoEnvs.gupshupEmail }}"
  GUPSHUP_PASSWORD: "{{ .Values.workersGoEnvs.gupshupPassword }}"
  FACEBOOK_APP_ID: "{{ .Values.workersGoEnvs.facebookAppId }}"
  DEFAULT_HSM_LIMIT: "{{ .Values.workersGoEnvs.defaultHsmLimit }}"
  DISABLE_WABA_WEBHOOK_URL_SET: "{{ .Values.workersGoEnvs.disableWabaWebhookUrlSet }}"
  DRIVERS_GATEWAY_URL: "{{ .Values.workersGoEnvs.driversGatewayUrl }}"
  QUEUE_MANAGER_DISPATCHER_URL: "{{ .Values.workersGoEnvs.queueManagerDispatcherUrl }}"
  MOCK_DRIVER_URL: "{{ .Values.workersGoEnvs.mockDriverUrl }}"
  USE_MOCK_DRIVER: "{{ .Values.workersGoEnvs.useMockDriver }}"
  REDIS_HOST: "dragonfly.dragonfly.svc.cluster.local"
  REDIS_PORT: "6379"
  REDIS_PASSWORD: ""
  REDIS_DB: "0"
  REDIS_URL: "redis://dragonfly.dragonfly.svc.cluster.local:6379"
  REDIS_ADDR: "dragonfly.dragonfly.svc.cluster.local:6379"
