apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-front-autoscaler
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-front-deployment
  minReplicas: {{ .Values.autoscaler.front.min }}
  maxReplicas: {{ .Values.autoscaler.front.max }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
