apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: migration-logs-pvc
  namespace: app
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: batch/v1
kind: Job
metadata:
    name: db-migrate
    namespace: {{ .Values.namespace }}
spec:
  activeDeadlineSeconds: 10000
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
    spec:
      containers:
      - command:
        - /bin/sh
        - -c
        - |
          TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
          LOG_FILE="/logs/db-migrate_${TIMESTAMP}.log"
          echo "=== DB Migration Started at $(date) ===" | tee $LOG_FILE
          node ./node_modules/.bin/sequelize db:migrate --debug 2>&1 | tee -a $LOG_FILE
          echo "=== DB Migration Finished at $(date) ===" | tee -a $LOG_FILE
          echo "Log saved to: $LOG_FILE"
        envFrom:
        - configMapRef:
            name: app-config-envs
        image: {{ .Values.imageDbMigration }}
        imagePullPolicy: "IfNotPresent"
        name: db-migrate
        volumeMounts:
        - name: logs-volume
          mountPath: /logs
      volumes:
      - name: logs-volume
        persistentVolumeClaim:
          claimName: migration-logs-pvc
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
      restartPolicy: Never
  ttlSecondsAfterFinished: 100