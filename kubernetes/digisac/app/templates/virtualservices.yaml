---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: app-api-virtual-service
  namespace: {{ .Values.namespace }}
spec:
  hosts:
  - {{ .Values.domain }}
  gateways:
  - gateway.default
  http:
    - match:
      - uri:
          prefix: "/api/"
      rewrite:
        uri: "/"
      route:
      - destination:
          host: app-api.{{ .Values.namespace }}.svc.cluster.local
          port:
            number: 80
    - match:
      - uri:
          prefix: "/socket.io/"
      route:
      - destination:
          host: app-socket-socket.{{ .Values.namespace }}.svc.cluster.local
          port:
            number: 80
    - match:
      - uri:
          prefix: "/"
      route:
      - destination:
          host: app-front.{{ .Values.namespace }}.svc.cluster.local
          port:
            number: 80
    - match:
        - uri:
            prefix: "/browserless/"
      route:
        - destination:
            host: app-browserless.{{ .Values.namespace }}.svc.cluster.local
            port:
              number: 80
