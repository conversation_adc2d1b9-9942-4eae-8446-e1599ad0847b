apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-browserless-autoscaler
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: app-browserless-statefulset
  minReplicas: {{ .Values.autoscaler.browserless.min }}
  maxReplicas: {{ .Values.autoscaler.browserless.max }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
