apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-api-autoscaler
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-api-deployment
  minReplicas: {{ .Values.autoscaler.api.min }}
  maxReplicas: {{ .Values.autoscaler.api.max }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
