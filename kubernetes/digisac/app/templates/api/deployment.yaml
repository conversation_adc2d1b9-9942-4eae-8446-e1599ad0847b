apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    configmap.reloader.stakater.com/reload: app-config-envs
  labels:
    app: app-api
    service: api
    configmap: app-config-envs
  name: app-api-deployment
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-api
  template:
    metadata:
      labels:
        app: app-api
      annotations:
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-api
      containers:
      - command:
        - /bin/sh
        - -c
        - exec node dist/microServices/api
        envFrom:
        - configMapRef:
            name: app-config-envs
        image: {{ .Values.imageApi }}
        imagePullPolicy: "IfNotPresent"
        name: app-api
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.api.limCpu }}
            memory: {{ .Values.resourceLimits.api.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.api.reqCpu }}
            memory: {{ .Values.resourceLimits.api.reqMem }}
        readinessProbe:
          httpGet:
            path: /v1
            port: 4002
          initialDelaySeconds: 5
          periodSeconds: 20
        ports:
        - containerPort: 4002
          protocol: TCP
      tolerations:
        - key: "oci.oraclecloud.com/oke-is-preemptible"
          operator: "Exists"
          effect: "NoSchedule"
      affinity:
        {{- if .Values.statelessNodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.statelessNodeAffinity.key }}
                operator: In
                values: {{ .Values.statelessNodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
