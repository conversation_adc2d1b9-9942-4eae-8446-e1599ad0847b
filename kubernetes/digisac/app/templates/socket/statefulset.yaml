apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: app-socket
    service: socket
    configmap: app-config-envs
  name: app-socket-statefulset
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-socket
  serviceName: app-socket
  template:
    metadata:
      labels:
        app: app-socket
      annotations:
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-socket
      containers:
      - command:
        - /bin/sh
        - -c
        - exec node dist/microServices/socket
        env:
        - name: SOCKET_GATEWAY_PORT
          value: "8080"
        - name: SOCKET_GATEWAY_HTTP_PORT
          value: "80"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: SOCKET_GATEWAY_ADDRESS
          value: http://$(POD_NAME).app-socket.{{ .Values.namespace }}.svc.cluster.local
        envFrom:
        - configMapRef:
            name: app-config-envs
        image: {{ .Values.imageSocket }}
        imagePullPolicy: "IfNotPresent"
        name: app-socket
        ports:
        - containerPort: 8080
          protocol: TCP
        - containerPort: 80
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.socket.limCpu }}
            memory: {{ .Values.resourceLimits.socket.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.socket.reqCpu }}
            memory: {{ .Values.resourceLimits.socket.reqMem }}
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 20
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
