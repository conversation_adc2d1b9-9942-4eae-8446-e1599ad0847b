apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-socket-autoscaler
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: app-socket-statefulset
  minReplicas: {{ .Values.autoscaler.socket.min }}
  maxReplicas: {{ .Values.autoscaler.socket.max }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
