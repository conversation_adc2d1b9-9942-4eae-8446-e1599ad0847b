apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    configmap.reloader.stakater.com/reload: app-config-envs
  labels:
    app: app-workers
    service: workers
    configmap: app-config-envs
  name: app-workers-deployment
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-workers
  template:
    metadata:
      labels:
        app: app-workers
      annotations:
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-workers
      containers:
      - command:
        - /bin/sh
        - -c
        - exec node {{- if .Values.resourceLimits.workers.nodeMem }} --max-old-space-size={{ .Values.resourceLimits.workers.nodeMem }}{{- end }} dist/microServices/workers
        env:
        - name: WORKERS_PORT
          value: "8080"
        envFrom:
        - configMapRef:
            name: app-config-envs
        image: {{ .Values.imageWorkers }}
        imagePullPolicy: "IfNotPresent"
        name: app-workers
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.workers.limCpu }}
            memory: {{ .Values.resourceLimits.workers.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.workers.reqCpu }}
            memory: {{ .Values.resourceLimits.workers.reqMem }}
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 20
        ports:
        - containerPort: 8080
          protocol: TCP
      tolerations:
        - key: "oci.oraclecloud.com/oke-is-preemptible"
          operator: "Exists"
          effect: "NoSchedule"
      affinity:
        {{- if .Values.statelessNodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.statelessNodeAffinity.key }}
                operator: In
                values: {{ .Values.statelessNodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}