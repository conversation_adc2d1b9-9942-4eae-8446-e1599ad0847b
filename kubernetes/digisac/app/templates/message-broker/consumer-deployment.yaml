apiVersion: apps/v1
kind: Deployment
metadata:
  name: message-broker-consumer
  namespace: {{ .Values.namespace }}
  labels:
    app: message-broker-consumer
    version: v1
spec:
  replicas: {{ .Values.autoscaler.messageBrokerConsumer.min }}
  selector:
    matchLabels:
      app: message-broker-consumer
      version: v1
  template:
    metadata:
      labels:
        app: message-broker-consumer
        version: v1
    spec:
      containers:
      - name: message-broker-consumer
        image: {{ .Values.imageMessageBrokerConsumer }}
        env:
        - name: RABBITMQ_HOST
          value: "lavinmq.tools.svc.cluster.local"
        volumeMounts:
        - name: queues-config
          mountPath: /app/config.yaml
          subPath: config.yaml
        resources:
          requests:
            memory: {{ .Values.resourceLimits.messageBrokerConsumer.reqMem }}
            cpu: {{ .Values.resourceLimits.messageBrokerConsumer.reqCpu }}
          limits:
            memory: {{ .Values.resourceLimits.messageBrokerConsumer.limMem }}
            cpu: {{ .Values.resourceLimits.messageBrokerConsumer.limCpu }}
      volumes:
      - name: queues-config
        configMap:
          name: message-broker-queues-config
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
