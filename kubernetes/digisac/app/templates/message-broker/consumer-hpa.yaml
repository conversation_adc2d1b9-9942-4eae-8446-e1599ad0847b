apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: message-broker-consumer-hpa
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: message-broker-consumer
  minReplicas: {{ .Values.autoscaler.messageBrokerConsumer.min }}
  maxReplicas: {{ .Values.autoscaler.messageBrokerConsumer.max }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
