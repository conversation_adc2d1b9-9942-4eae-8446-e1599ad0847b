namespace: tools

image:
  repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/postgres
  tag: "16-alpine"
  pullPolicy: IfNotPresent

replicaCount: 1

service:
  type: ClusterIP
  port: 5432

resources:
  requests:
    memory: "512Mi"
    cpu: "200m"
  limits:
    memory: "1Gi"
    cpu: "500m"

persistence:
  enabled: true
  storageClass: "oci-bv"
  size: 50Gi
  accessMode: ReadWriteOnce

env:
  POSTGRES_USER: "postgres"
  POSTGRES_PASSWORD: "LvJNqnD5cXxvcCFXu5DWv8KxHkxaFuMP"
  POSTGRES_DB: "mandeumzap"

nodeSelector:
  pool: tools

tolerations:
  - key: "pool"
    operator: "Equal"
    value: "tools"
    effect: "NoSchedule"

healthcheck:
  enabled: true
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 10
  failureThreshold: 5

sharedMemory:
  size: "512Mi"

command:
  - postgres
  - -c
  - config_file=/etc/postgresql-config/postgresql.conf
  - -c
  - hba_file=/etc/postgresql-config/pg_hba.conf

configFiles:
  enabled: false  # Set to true if you want to mount custom config files
