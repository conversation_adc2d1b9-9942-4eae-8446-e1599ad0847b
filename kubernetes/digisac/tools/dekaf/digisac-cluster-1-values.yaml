replicaCount: 1

image:
  repository: tealtools/dekaf:latest
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 8090

resources:
  requests:
    memory: "256Mi"
  limits:
    memory: "512Mi"

pulsar:
  brokerUrl: "pulsar://pulsar-proxy.pulsar.svc.cluster.local:6650"
  brokerWebUrl: "http://pulsar-proxy.pulsar.svc.cluster.local:8080"

storage:
  enabled: true
  size: 10Gi
  storageClass: oci-bv
  accessMode: ReadWriteOnce

namespace: pulsar
