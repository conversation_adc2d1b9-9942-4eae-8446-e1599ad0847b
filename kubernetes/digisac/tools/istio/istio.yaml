apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  namespace: istio-system
  name: istio 
spec:
  profile: ambient
  components:
    cni: # Precisa habilitar no CentOS para funcionar tabela iptable_nat
      enabled: true
    ingressGateways:
      - name: istio-ingressgateway
        enabled: true
        k8s:
          nodeSelector:
            pool: tools
          tolerations:
            - key: "pool"
              operator: "Equal"
              value: "tools"
              effect: "NoSchedule"
          hpaSpec:
            minReplicas: 2
            maxReplicas: 5
          serviceAnnotations:
            # disable automatic security list update, we manage it trough terraform
            service.beta.kubernetes.io/oci-load-balancer-security-list-management-mode: "None"
            service.beta.kubernetes.io/oci-load-balancer-shape: "flexible"
            service.beta.kubernetes.io/oci-load-balancer-shape-flex-min: "10"
            service.beta.kubernetes.io/oci-load-balancer-shape-flex-max: "1000"
          service:
            type: LoadBalancer
            ports:
              - port: 15021
                targetPort: 15021
                name: status-port
                protocol: TCP
              - port: 80
                targetPort: 8080
                name: http2
                protocol: TCP
              #- port: 443
              #  targetPort: 8443
              #  name: https
              #  protocol: TCP
              - port: 9187
                targetPort: 9187
                name: postgres-exporter
                protocol: TCP
          affinity:
            podAntiAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
              - labelSelector:
                  matchExpressions:
                  - key: app
                    operator: In
                    values:
                    - istio-ingressgateway
                topologyKey: "kubernetes.io/hostname"
