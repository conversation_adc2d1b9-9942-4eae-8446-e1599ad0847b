apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: gateway
spec:
  selector:
    istio: ingressgateway
  servers:
{{/*  - port:*/}}
{{/*      number: 9187*/}}
{{/*      name: http*/}}
{{/*      protocol: HTTP*/}}
{{/*    hosts:*/}}
{{/*    - "exporters-oke-digisac.digisac.io"*/}}
  - port:
      number: 80
      name: http2
      protocol: HTTP2
    hosts:
      {{- range .Values.gatewayHosts }}
        - {{ . | quote }}
      {{- end }}
  #- port:
  #    number: 443
  #    name: https
  #    protocol: HTTPS
  #  hosts: {{ .Values.gatewayHosts }}
  #  tls:
  #    mode: SIMPLE
  #    credentialName: istio-cert-digisac-io
  #    httpsRedirect: true # DEIXAR COMO TRUE SE FOR UTILIZAR SEM O PROXY CLOUDFLARE