OCIR credentials have been installed!

The secret "{{ .Values.secretName }}" has been created in the "{{ .Values.namespace }}" namespace.

To use these credentials in your deployments, add this to your pod spec:
```yaml
imagePullSecrets:
- name: {{ .Values.secretName }}
```

To set this as the default for your namespace:
```bash
kubectl patch serviceaccount default -p '{"imagePullSecrets":[{"name":"{{ .Values.secretName }}"}]}'
```