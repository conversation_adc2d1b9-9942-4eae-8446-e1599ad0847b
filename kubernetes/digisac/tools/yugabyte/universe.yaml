apiVersion: operator.yugabyte.io/v1alpha1
kind: YBUniverse
metadata:
  name: yugabyte-test
spec:
  numNodes: 3
  replicationFactor: 1
  enableYSQL: true
  enableNodeToNodeEncrypt: false
  enableClientToNodeEncrypt: false
  enableLoadBalancer: false
  ybSoftwareVersion: "2024.2"
  enableYSQLAuth: false
  enableYCQL: false
  enableYCQLAuth: false
  gFlags:
    tserverGFlags: {}
    masterGFlags: {}
  deviceInfo:
    volumeSize: 400
    numVolumes: 1
    storageClass: ""
  kubernetesOverrides:
    resource:
      master:
        requests:
          cpu: 2
          memory: 8Gi
        limits:
          cpu: 3
          memory: 8Gi